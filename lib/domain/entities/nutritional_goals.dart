class NutritionalGoals {
  final String id;
  final String userId;
  final double dailyCalories;
  final double dailyProtein; // grams
  final double dailyCarbs; // grams
  final double dailyFats; // grams
  final double dailyFiber; // grams
  final double dailySugar; // grams
  final double dailySodium; // mg
  final double dailyVitaminA; // mcg
  final double dailyVitaminC; // mg
  final double dailyVitaminD; // mcg
  final double dailyCalcium; // mg
  final double dailyIron; // mg
  final DateTime createdAt;
  final DateTime updatedAt;
  
  const NutritionalGoals({
    required this.id,
    required this.userId,
    required this.dailyCalories,
    required this.dailyProtein,
    required this.dailyCarbs,
    required this.dailyFats,
    required this.dailyFiber,
    required this.dailySugar,
    required this.dailySodium,
    required this.dailyVitaminA,
    required this.dailyVitaminC,
    required this.dailyVitaminD,
    required this.dailyCalcium,
    required this.dailyIron,
    required this.createdAt,
    required this.updatedAt,
  });
  
  NutritionalGoals copyWith({
    String? id,
    String? userId,
    double? dailyCalories,
    double? dailyProtein,
    double? dailyCarbs,
    double? dailyFats,
    double? dailyFiber,
    double? dailySugar,
    double? dailySodium,
    double? dailyVitaminA,
    double? dailyVitaminC,
    double? dailyVitaminD,
    double? dailyCalcium,
    double? dailyIron,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return NutritionalGoals(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      dailyCalories: dailyCalories ?? this.dailyCalories,
      dailyProtein: dailyProtein ?? this.dailyProtein,
      dailyCarbs: dailyCarbs ?? this.dailyCarbs,
      dailyFats: dailyFats ?? this.dailyFats,
      dailyFiber: dailyFiber ?? this.dailyFiber,
      dailySugar: dailySugar ?? this.dailySugar,
      dailySodium: dailySodium ?? this.dailySodium,
      dailyVitaminA: dailyVitaminA ?? this.dailyVitaminA,
      dailyVitaminC: dailyVitaminC ?? this.dailyVitaminC,
      dailyVitaminD: dailyVitaminD ?? this.dailyVitaminD,
      dailyCalcium: dailyCalcium ?? this.dailyCalcium,
      dailyIron: dailyIron ?? this.dailyIron,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
  
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is NutritionalGoals &&
           other.id == id &&
           other.userId == userId &&
           other.dailyCalories == dailyCalories &&
           other.dailyProtein == dailyProtein &&
           other.dailyCarbs == dailyCarbs &&
           other.dailyFats == dailyFats &&
           other.dailyFiber == dailyFiber &&
           other.dailySugar == dailySugar &&
           other.dailySodium == dailySodium &&
           other.dailyVitaminA == dailyVitaminA &&
           other.dailyVitaminC == dailyVitaminC &&
           other.dailyVitaminD == dailyVitaminD &&
           other.dailyCalcium == dailyCalcium &&
           other.dailyIron == dailyIron &&
           other.createdAt == createdAt &&
           other.updatedAt == updatedAt;
  }
  
  @override
  int get hashCode {
    return Object.hash(
      id,
      userId,
      dailyCalories,
      dailyProtein,
      dailyCarbs,
      dailyFats,
      dailyFiber,
      dailySugar,
      dailySodium,
      dailyVitaminA,
      dailyVitaminC,
      dailyVitaminD,
      dailyCalcium,
      dailyIron,
      createdAt,
      updatedAt,
    );
  }
  
  @override
  String toString() {
    return 'NutritionalGoals(id: $id, userId: $userId, dailyCalories: $dailyCalories, '
           'dailyProtein: $dailyProtein, dailyCarbs: $dailyCarbs, dailyFats: $dailyFats, '
           'dailyFiber: $dailyFiber, dailySugar: $dailySugar, dailySodium: $dailySodium, '
           'dailyVitaminA: $dailyVitaminA, dailyVitaminC: $dailyVitaminC, '
           'dailyVitaminD: $dailyVitaminD, dailyCalcium: $dailyCalcium, '
           'dailyIron: $dailyIron, createdAt: $createdAt, updatedAt: $updatedAt)';
  }
}
