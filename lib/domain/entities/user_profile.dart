class UserProfile {
  final String id;
  final String name;
  final int age;
  final String gender; // 'male' or 'female'
  final double weight; // in kg
  final double height; // in cm
  final String activityLevel; // 'sedentary', 'lightly_active', etc.
  final String goal; // 'lose', 'maintain', 'gain'
  final String weightUnit; // 'kg' or 'lbs'
  final String heightUnit; // 'cm' or 'ft'
  final DateTime createdAt;
  final DateTime updatedAt;
  
  const UserProfile({
    required this.id,
    required this.name,
    required this.age,
    required this.gender,
    required this.weight,
    required this.height,
    required this.activityLevel,
    required this.goal,
    required this.weightUnit,
    required this.heightUnit,
    required this.createdAt,
    required this.updatedAt,
  });
  
  UserProfile copyWith({
    String? id,
    String? name,
    int? age,
    String? gender,
    double? weight,
    double? height,
    String? activityLevel,
    String? goal,
    String? weightUnit,
    String? heightUnit,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return UserProfile(
      id: id ?? this.id,
      name: name ?? this.name,
      age: age ?? this.age,
      gender: gender ?? this.gender,
      weight: weight ?? this.weight,
      height: height ?? this.height,
      activityLevel: activityLevel ?? this.activityLevel,
      goal: goal ?? this.goal,
      weightUnit: weightUnit ?? this.weightUnit,
      heightUnit: heightUnit ?? this.heightUnit,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
  
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserProfile &&
           other.id == id &&
           other.name == name &&
           other.age == age &&
           other.gender == gender &&
           other.weight == weight &&
           other.height == height &&
           other.activityLevel == activityLevel &&
           other.goal == goal &&
           other.weightUnit == weightUnit &&
           other.heightUnit == heightUnit &&
           other.createdAt == createdAt &&
           other.updatedAt == updatedAt;
  }
  
  @override
  int get hashCode {
    return Object.hash(
      id,
      name,
      age,
      gender,
      weight,
      height,
      activityLevel,
      goal,
      weightUnit,
      heightUnit,
      createdAt,
      updatedAt,
    );
  }
  
  @override
  String toString() {
    return 'UserProfile(id: $id, name: $name, age: $age, gender: $gender, '
           'weight: $weight, height: $height, activityLevel: $activityLevel, '
           'goal: $goal, weightUnit: $weightUnit, heightUnit: $heightUnit, '
           'createdAt: $createdAt, updatedAt: $updatedAt)';
  }
}
