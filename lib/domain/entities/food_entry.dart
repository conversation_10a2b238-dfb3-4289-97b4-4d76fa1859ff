import 'nutrition_data.dart';

class FoodEntry {
  final String id;
  final String userId;
  final String name;
  final String? description;
  final String? brand;
  final double servingSize;
  final String servingUnit;
  final NutritionData nutritionData;
  final String? imagePath;
  final String mealType; // 'breakfast', 'lunch', 'dinner', 'snack'
  final DateTime consumedAt;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String? notes;
  final bool isAnalyzedByAI;
  final double? confidence; // AI analysis confidence score (0.0 - 1.0)
  
  const FoodEntry({
    required this.id,
    required this.userId,
    required this.name,
    this.description,
    this.brand,
    required this.servingSize,
    required this.servingUnit,
    required this.nutritionData,
    this.imagePath,
    required this.mealType,
    required this.consumedAt,
    required this.createdAt,
    required this.updatedAt,
    this.notes,
    this.isAnalyzedByAI = false,
    this.confidence,
  });
  
  FoodEntry copyWith({
    String? id,
    String? userId,
    String? name,
    String? description,
    String? brand,
    double? servingSize,
    String? servingUnit,
    NutritionData? nutritionData,
    String? imagePath,
    String? mealType,
    DateTime? consumedAt,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? notes,
    bool? isAnalyzedByAI,
    double? confidence,
  }) {
    return FoodEntry(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      name: name ?? this.name,
      description: description ?? this.description,
      brand: brand ?? this.brand,
      servingSize: servingSize ?? this.servingSize,
      servingUnit: servingUnit ?? this.servingUnit,
      nutritionData: nutritionData ?? this.nutritionData,
      imagePath: imagePath ?? this.imagePath,
      mealType: mealType ?? this.mealType,
      consumedAt: consumedAt ?? this.consumedAt,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      notes: notes ?? this.notes,
      isAnalyzedByAI: isAnalyzedByAI ?? this.isAnalyzedByAI,
      confidence: confidence ?? this.confidence,
    );
  }
  
  /// Get nutrition data adjusted for actual serving size
  NutritionData getAdjustedNutrition({double? actualServingSize}) {
    if (actualServingSize == null || actualServingSize == servingSize) {
      return nutritionData;
    }
    
    final factor = actualServingSize / servingSize;
    return nutritionData * factor;
  }
  
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is FoodEntry &&
           other.id == id &&
           other.userId == userId &&
           other.name == name &&
           other.description == description &&
           other.brand == brand &&
           other.servingSize == servingSize &&
           other.servingUnit == servingUnit &&
           other.nutritionData == nutritionData &&
           other.imagePath == imagePath &&
           other.mealType == mealType &&
           other.consumedAt == consumedAt &&
           other.createdAt == createdAt &&
           other.updatedAt == updatedAt &&
           other.notes == notes &&
           other.isAnalyzedByAI == isAnalyzedByAI &&
           other.confidence == confidence;
  }
  
  @override
  int get hashCode {
    return Object.hash(
      id,
      userId,
      name,
      description,
      brand,
      servingSize,
      servingUnit,
      nutritionData,
      imagePath,
      mealType,
      consumedAt,
      createdAt,
      updatedAt,
      notes,
      isAnalyzedByAI,
      confidence,
    );
  }
  
  @override
  String toString() {
    return 'FoodEntry(id: $id, userId: $userId, name: $name, description: $description, '
           'brand: $brand, servingSize: $servingSize, servingUnit: $servingUnit, '
           'nutritionData: $nutritionData, imagePath: $imagePath, mealType: $mealType, '
           'consumedAt: $consumedAt, createdAt: $createdAt, updatedAt: $updatedAt, '
           'notes: $notes, isAnalyzedByAI: $isAnalyzedByAI, confidence: $confidence)';
  }
}
