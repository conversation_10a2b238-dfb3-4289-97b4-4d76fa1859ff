import 'nutrition_data.dart';
import 'food_entry.dart';

class DailyLog {
  final String id;
  final String userId;
  final DateTime date;
  final List<FoodEntry> foodEntries;
  final NutritionData totalNutrition;
  final double? weight; // Optional daily weight tracking
  final String? notes;
  final DateTime createdAt;
  final DateTime updatedAt;
  
  const DailyLog({
    required this.id,
    required this.userId,
    required this.date,
    required this.foodEntries,
    required this.totalNutrition,
    this.weight,
    this.notes,
    required this.createdAt,
    required this.updatedAt,
  });
  
  factory DailyLog.empty({
    required String id,
    required String userId,
    required DateTime date,
  }) {
    final now = DateTime.now();
    return DailyLog(
      id: id,
      userId: userId,
      date: date,
      foodEntries: const [],
      totalNutrition: NutritionData.empty(),
      createdAt: now,
      updatedAt: now,
    );
  }
  
  DailyLog copyWith({
    String? id,
    String? userId,
    DateTime? date,
    List<FoodEntry>? foodEntries,
    NutritionData? totalNutrition,
    double? weight,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return DailyLog(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      date: date ?? this.date,
      foodEntries: foodEntries ?? this.foodEntries,
      totalNutrition: totalNutrition ?? this.totalNutrition,
      weight: weight ?? this.weight,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
  
  /// Calculate total nutrition from all food entries
  NutritionData calculateTotalNutrition() {
    if (foodEntries.isEmpty) {
      return NutritionData.empty();
    }
    
    return foodEntries
        .map((entry) => entry.nutritionData)
        .reduce((total, nutrition) => total + nutrition);
  }
  
  /// Add a food entry to the daily log
  DailyLog addFoodEntry(FoodEntry entry) {
    final updatedEntries = [...foodEntries, entry];
    final updatedNutrition = calculateTotalNutritionFromEntries(updatedEntries);
    
    return copyWith(
      foodEntries: updatedEntries,
      totalNutrition: updatedNutrition,
      updatedAt: DateTime.now(),
    );
  }
  
  /// Remove a food entry from the daily log
  DailyLog removeFoodEntry(String entryId) {
    final updatedEntries = foodEntries.where((entry) => entry.id != entryId).toList();
    final updatedNutrition = calculateTotalNutritionFromEntries(updatedEntries);
    
    return copyWith(
      foodEntries: updatedEntries,
      totalNutrition: updatedNutrition,
      updatedAt: DateTime.now(),
    );
  }
  
  /// Update a food entry in the daily log
  DailyLog updateFoodEntry(FoodEntry updatedEntry) {
    final updatedEntries = foodEntries
        .map((entry) => entry.id == updatedEntry.id ? updatedEntry : entry)
        .toList();
    final updatedNutrition = calculateTotalNutritionFromEntries(updatedEntries);
    
    return copyWith(
      foodEntries: updatedEntries,
      totalNutrition: updatedNutrition,
      updatedAt: DateTime.now(),
    );
  }
  
  /// Get food entries by meal type
  List<FoodEntry> getFoodEntriesByMealType(String mealType) {
    return foodEntries.where((entry) => entry.mealType == mealType).toList();
  }
  
  /// Get nutrition data by meal type
  NutritionData getNutritionByMealType(String mealType) {
    final mealEntries = getFoodEntriesByMealType(mealType);
    if (mealEntries.isEmpty) {
      return NutritionData.empty();
    }
    
    return mealEntries
        .map((entry) => entry.nutritionData)
        .reduce((total, nutrition) => total + nutrition);
  }
  
  /// Calculate total nutrition from a list of entries
  static NutritionData calculateTotalNutritionFromEntries(List<FoodEntry> entries) {
    if (entries.isEmpty) {
      return NutritionData.empty();
    }
    
    return entries
        .map((entry) => entry.nutritionData)
        .reduce((total, nutrition) => total + nutrition);
  }
  
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is DailyLog &&
           other.id == id &&
           other.userId == userId &&
           other.date == date &&
           other.foodEntries == foodEntries &&
           other.totalNutrition == totalNutrition &&
           other.weight == weight &&
           other.notes == notes &&
           other.createdAt == createdAt &&
           other.updatedAt == updatedAt;
  }
  
  @override
  int get hashCode {
    return Object.hash(
      id,
      userId,
      date,
      foodEntries,
      totalNutrition,
      weight,
      notes,
      createdAt,
      updatedAt,
    );
  }
  
  @override
  String toString() {
    return 'DailyLog(id: $id, userId: $userId, date: $date, '
           'foodEntries: ${foodEntries.length} entries, totalNutrition: $totalNutrition, '
           'weight: $weight, notes: $notes, createdAt: $createdAt, updatedAt: $updatedAt)';
  }
}
