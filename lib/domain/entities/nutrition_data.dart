class NutritionData {
  final double calories;
  final double protein; // grams
  final double carbs; // grams
  final double fats; // grams
  final double fiber; // grams
  final double sugar; // grams
  final double sodium; // mg
  final double vitaminA; // mcg
  final double vitaminC; // mg
  final double vitaminD; // mcg
  final double calcium; // mg
  final double iron; // mg
  final double saturatedFat; // grams
  final double transFat; // grams
  final double cholesterol; // mg
  final double potassium; // mg

  const NutritionData({
    required this.calories,
    required this.protein,
    required this.carbs,
    required this.fats,
    this.fiber = 0.0,
    this.sugar = 0.0,
    this.sodium = 0.0,
    this.vitaminA = 0.0,
    this.vitaminC = 0.0,
    this.vitaminD = 0.0,
    this.calcium = 0.0,
    this.iron = 0.0,
    this.saturatedFat = 0.0,
    this.transFat = 0.0,
    this.cholesterol = 0.0,
    this.potassium = 0.0,
  });

  factory NutritionData.empty() {
    return const NutritionData(
      calories: 0.0,
      protein: 0.0,
      carbs: 0.0,
      fats: 0.0,
    );
  }

  NutritionData copyWith({
    double? calories,
    double? protein,
    double? carbs,
    double? fats,
    double? fiber,
    double? sugar,
    double? sodium,
    double? vitaminA,
    double? vitaminC,
    double? vitaminD,
    double? calcium,
    double? iron,
    double? saturatedFat,
    double? transFat,
    double? cholesterol,
    double? potassium,
  }) {
    return NutritionData(
      calories: calories ?? this.calories,
      protein: protein ?? this.protein,
      carbs: carbs ?? this.carbs,
      fats: fats ?? this.fats,
      fiber: fiber ?? this.fiber,
      sugar: sugar ?? this.sugar,
      sodium: sodium ?? this.sodium,
      vitaminA: vitaminA ?? this.vitaminA,
      vitaminC: vitaminC ?? this.vitaminC,
      vitaminD: vitaminD ?? this.vitaminD,
      calcium: calcium ?? this.calcium,
      iron: iron ?? this.iron,
      saturatedFat: saturatedFat ?? this.saturatedFat,
      transFat: transFat ?? this.transFat,
      cholesterol: cholesterol ?? this.cholesterol,
      potassium: potassium ?? this.potassium,
    );
  }

  /// Add nutrition data from another source
  NutritionData operator +(NutritionData other) {
    return NutritionData(
      calories: calories + other.calories,
      protein: protein + other.protein,
      carbs: carbs + other.carbs,
      fats: fats + other.fats,
      fiber: fiber + other.fiber,
      sugar: sugar + other.sugar,
      sodium: sodium + other.sodium,
      vitaminA: vitaminA + other.vitaminA,
      vitaminC: vitaminC + other.vitaminC,
      vitaminD: vitaminD + other.vitaminD,
      calcium: calcium + other.calcium,
      iron: iron + other.iron,
      saturatedFat: saturatedFat + other.saturatedFat,
      transFat: transFat + other.transFat,
      cholesterol: cholesterol + other.cholesterol,
      potassium: potassium + other.potassium,
    );
  }

  /// Multiply nutrition data by a factor (for serving size adjustments)
  NutritionData operator *(double factor) {
    return NutritionData(
      calories: calories * factor,
      protein: protein * factor,
      carbs: carbs * factor,
      fats: fats * factor,
      fiber: fiber * factor,
      sugar: sugar * factor,
      sodium: sodium * factor,
      vitaminA: vitaminA * factor,
      vitaminC: vitaminC * factor,
      vitaminD: vitaminD * factor,
      calcium: calcium * factor,
      iron: iron * factor,
      saturatedFat: saturatedFat * factor,
      transFat: transFat * factor,
      cholesterol: cholesterol * factor,
      potassium: potassium * factor,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is NutritionData &&
        other.calories == calories &&
        other.protein == protein &&
        other.carbs == carbs &&
        other.fats == fats &&
        other.fiber == fiber &&
        other.sugar == sugar &&
        other.sodium == sodium &&
        other.vitaminA == vitaminA &&
        other.vitaminC == vitaminC &&
        other.vitaminD == vitaminD &&
        other.calcium == calcium &&
        other.iron == iron &&
        other.saturatedFat == saturatedFat &&
        other.transFat == transFat &&
        other.cholesterol == cholesterol &&
        other.potassium == potassium;
  }

  @override
  int get hashCode {
    return Object.hash(
      calories,
      protein,
      carbs,
      fats,
      fiber,
      sugar,
      sodium,
      vitaminA,
      vitaminC,
      vitaminD,
      calcium,
      iron,
      saturatedFat,
      transFat,
      cholesterol,
      potassium,
    );
  }

  @override
  String toString() {
    return 'NutritionData(calories: $calories, protein: $protein, carbs: $carbs, '
        'fats: $fats, fiber: $fiber, sugar: $sugar, sodium: $sodium, '
        'vitaminA: $vitaminA, vitaminC: $vitaminC, vitaminD: $vitaminD, '
        'calcium: $calcium, iron: $iron, saturatedFat: $saturatedFat, '
        'transFat: $transFat, cholesterol: $cholesterol, potassium: $potassium)';
  }

  /// Convert to JSON map
  Map<String, dynamic> toJson() {
    return {
      'calories': calories,
      'protein': protein,
      'carbs': carbs,
      'fats': fats,
      'fiber': fiber,
      'sugar': sugar,
      'sodium': sodium,
      'vitaminA': vitaminA,
      'vitaminC': vitaminC,
      'vitaminD': vitaminD,
      'calcium': calcium,
      'iron': iron,
      'saturatedFat': saturatedFat,
      'transFat': transFat,
      'cholesterol': cholesterol,
      'potassium': potassium,
    };
  }

  /// Create from JSON map
  factory NutritionData.fromJson(Map<String, dynamic> json) {
    return NutritionData(
      calories: (json['calories'] as num?)?.toDouble() ?? 0.0,
      protein: (json['protein'] as num?)?.toDouble() ?? 0.0,
      carbs: (json['carbs'] as num?)?.toDouble() ?? 0.0,
      fats: (json['fats'] as num?)?.toDouble() ?? 0.0,
      fiber: (json['fiber'] as num?)?.toDouble() ?? 0.0,
      sugar: (json['sugar'] as num?)?.toDouble() ?? 0.0,
      sodium: (json['sodium'] as num?)?.toDouble() ?? 0.0,
      vitaminA: (json['vitaminA'] as num?)?.toDouble() ?? 0.0,
      vitaminC: (json['vitaminC'] as num?)?.toDouble() ?? 0.0,
      vitaminD: (json['vitaminD'] as num?)?.toDouble() ?? 0.0,
      calcium: (json['calcium'] as num?)?.toDouble() ?? 0.0,
      iron: (json['iron'] as num?)?.toDouble() ?? 0.0,
      saturatedFat: (json['saturatedFat'] as num?)?.toDouble() ?? 0.0,
      transFat: (json['transFat'] as num?)?.toDouble() ?? 0.0,
      cholesterol: (json['cholesterol'] as num?)?.toDouble() ?? 0.0,
      potassium: (json['potassium'] as num?)?.toDouble() ?? 0.0,
    );
  }
}
