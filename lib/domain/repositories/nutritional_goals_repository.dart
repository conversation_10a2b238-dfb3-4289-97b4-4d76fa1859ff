import '../entities/nutritional_goals.dart';
import '../../core/utils/result.dart';

abstract class NutritionalGoalsRepository {
  Future<Result<NutritionalGoals>> createNutritionalGoals(
    NutritionalGoals goals,
  );
  Future<Result<NutritionalGoals?>> getNutritionalGoals(String userId);
  Future<Result<NutritionalGoals>> updateNutritionalGoals(
    NutritionalGoals goals,
  );
  Future<Result<void>> deleteNutritionalGoals(String userId);
}
