import '../entities/user_profile.dart';
import '../../core/utils/result.dart';

abstract class UserProfileRepository {
  Future<Result<UserProfile>> createUserProfile(UserProfile profile);
  Future<Result<UserProfile?>> getUserProfile(String userId);
  Future<Result<UserProfile>> updateUserProfile(UserProfile profile);
  Future<Result<void>> deleteUserProfile(String userId);
  Future<Result<bool>> hasUserProfile();
  Future<Result<UserProfile?>> getCurrentUserProfile();
}
