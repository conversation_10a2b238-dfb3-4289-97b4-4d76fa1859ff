import 'dart:io';
import '../entities/food_entry.dart';
import '../../core/utils/result.dart';
import '../../core/errors/failures.dart';
import '../../data/datasources/gemini_api_service.dart';

class AnalyzeFoodImageUseCase {
  final GeminiApiService _geminiApiService;

  AnalyzeFoodImageUseCase(this._geminiApiService);

  Future<Result<FoodEntry>> execute(AnalyzeFoodImageParams params) async {
    try {
      // Validate image file
      if (!await params.imageFile.exists()) {
        return const ResultFailure(
          FileSystemFailure(message: 'Image file does not exist'),
        );
      }

      // Check file size
      final fileSize = await params.imageFile.length();
      if (fileSize > 5 * 1024 * 1024) {
        // 5MB limit
        return const ResultFailure(
          FileSystemFailure(message: 'Image file is too large (max 5MB)'),
        );
      }

      // Analyze image with Gemini API
      final analysisResult = await _geminiApiService.analyzeFoodImage(
        params.imageFile,
      );

      if (analysisResult.isFailure) {
        return ResultFailure(analysisResult.failureValue!);
      }

      final analysis = analysisResult.successValue!;
      final now = DateTime.now();

      // Create food entry from analysis
      final foodEntry = FoodEntry(
        id: params.entryId,
        userId: params.userId,
        name: analysis.foodName,
        description: analysis.description,
        brand: analysis.brand,
        servingSize: analysis.servingSize,
        servingUnit: analysis.servingUnit,
        nutritionData: analysis.nutritionData,
        imagePath: params.imageFile.path,
        mealType: params.mealType,
        consumedAt: params.consumedAt ?? now,
        createdAt: now,
        updatedAt: now,
        notes: params.notes,
        isAnalyzedByAI: true,
        confidence: analysis.confidence,
      );

      return Success(foodEntry);
    } catch (e) {
      return ResultFailure(
        ApiFailure(message: 'Failed to analyze food image: $e'),
      );
    }
  }
}

class AnalyzeFoodImageParams {
  final String entryId;
  final String userId;
  final File imageFile;
  final String mealType;
  final DateTime? consumedAt;
  final String? notes;

  const AnalyzeFoodImageParams({
    required this.entryId,
    required this.userId,
    required this.imageFile,
    required this.mealType,
    this.consumedAt,
    this.notes,
  });
}
