import '../entities/user_profile.dart';
import '../entities/nutritional_goals.dart';
import '../repositories/user_profile_repository.dart';
import '../repositories/nutritional_goals_repository.dart';
import '../../core/utils/result.dart';
import '../../core/utils/nutrition_calculator.dart';
import '../../core/errors/failures.dart';

class CreateUserProfileUseCase {
  final UserProfileRepository _userProfileRepository;
  final NutritionalGoalsRepository _nutritionalGoalsRepository;

  CreateUserProfileUseCase(
    this._userProfileRepository,
    this._nutritionalGoalsRepository,
  );

  Future<Result<UserProfile>> execute(CreateUserProfileParams params) async {
    try {
      // Validate input
      final validationResult = _validateParams(params);
      if (validationResult != null) {
        return ResultFailure(validationResult);
      }

      final now = DateTime.now();
      
      // Create user profile
      final userProfile = UserProfile(
        id: params.id,
        name: params.name,
        age: params.age,
        gender: params.gender,
        weight: params.weight,
        height: params.height,
        activityLevel: params.activityLevel,
        goal: params.goal,
        weightUnit: params.weightUnit,
        heightUnit: params.heightUnit,
        createdAt: now,
        updatedAt: now,
      );

      // Save user profile
      final profileResult = await _userProfileRepository.createUserProfile(userProfile);
      if (profileResult.isFailure) {
        return ResultFailure(profileResult.failureValue!);
      }

      // Calculate and create nutritional goals
      final nutritionalGoals = _calculateNutritionalGoals(userProfile);
      final goalsResult = await _nutritionalGoalsRepository.createNutritionalGoals(nutritionalGoals);
      
      if (goalsResult.isFailure) {
        // Rollback user profile creation
        await _userProfileRepository.deleteUserProfile(userProfile.id);
        return ResultFailure(goalsResult.failureValue!);
      }

      return Success(userProfile);
    } catch (e) {
      return ResultFailure(ValidationFailure(message: 'Failed to create user profile: $e'));
    }
  }

  ValidationFailure? _validateParams(CreateUserProfileParams params) {
    if (params.name.trim().isEmpty) {
      return const ValidationFailure(message: 'Name cannot be empty');
    }
    
    if (params.age < 13 || params.age > 120) {
      return const ValidationFailure(message: 'Age must be between 13 and 120');
    }
    
    if (!['male', 'female'].contains(params.gender.toLowerCase())) {
      return const ValidationFailure(message: 'Gender must be male or female');
    }
    
    if (params.weight <= 0 || params.weight > 1000) {
      return const ValidationFailure(message: 'Weight must be between 0 and 1000');
    }
    
    if (params.height <= 0 || params.height > 300) {
      return const ValidationFailure(message: 'Height must be between 0 and 300');
    }
    
    const validActivityLevels = [
      'sedentary',
      'lightly_active',
      'moderately_active',
      'very_active',
      'extremely_active'
    ];
    
    if (!validActivityLevels.contains(params.activityLevel)) {
      return const ValidationFailure(message: 'Invalid activity level');
    }
    
    const validGoals = ['lose', 'maintain', 'gain'];
    if (!validGoals.contains(params.goal)) {
      return const ValidationFailure(message: 'Invalid goal');
    }

    return null;
  }

  NutritionalGoals _calculateNutritionalGoals(UserProfile profile) {
    final dailyCalories = NutritionCalculator.calculateDailyCalories(
      weight: profile.weight,
      height: profile.height,
      age: profile.age,
      gender: profile.gender,
      activityLevel: profile.activityLevel,
      goal: profile.goal,
    );

    final dailyProtein = NutritionCalculator.calculateProteinNeeds(
      weight: profile.weight,
      activityLevel: profile.activityLevel,
    );

    final dailyCarbs = NutritionCalculator.calculateCarbNeeds(
      dailyCalories: dailyCalories,
    );

    final dailyFats = NutritionCalculator.calculateFatNeeds(
      dailyCalories: dailyCalories,
    );

    final dailyFiber = NutritionCalculator.calculateFiberNeeds(
      age: profile.age,
      gender: profile.gender,
    );

    final now = DateTime.now();

    return NutritionalGoals(
      id: '${profile.id}_goals',
      userId: profile.id,
      dailyCalories: dailyCalories,
      dailyProtein: dailyProtein,
      dailyCarbs: dailyCarbs,
      dailyFats: dailyFats,
      dailyFiber: dailyFiber,
      dailySugar: 50.0, // Default recommended limit
      dailySodium: 2300.0, // Default recommended limit (mg)
      dailyVitaminA: profile.gender.toLowerCase() == 'male' ? 900.0 : 700.0, // mcg
      dailyVitaminC: profile.gender.toLowerCase() == 'male' ? 90.0 : 75.0, // mg
      dailyVitaminD: 20.0, // mcg
      dailyCalcium: profile.age <= 50 ? 1000.0 : 1200.0, // mg
      dailyIron: profile.gender.toLowerCase() == 'male' ? 8.0 : 18.0, // mg
      createdAt: now,
      updatedAt: now,
    );
  }
}

class CreateUserProfileParams {
  final String id;
  final String name;
  final int age;
  final String gender;
  final double weight;
  final double height;
  final String activityLevel;
  final String goal;
  final String weightUnit;
  final String heightUnit;

  const CreateUserProfileParams({
    required this.id,
    required this.name,
    required this.age,
    required this.gender,
    required this.weight,
    required this.height,
    required this.activityLevel,
    required this.goal,
    required this.weightUnit,
    required this.heightUnit,
  });
}
