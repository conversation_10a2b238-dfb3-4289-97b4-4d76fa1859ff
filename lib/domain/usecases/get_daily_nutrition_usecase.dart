import '../entities/daily_log.dart';
import '../entities/food_entry.dart';
import '../entities/nutritional_goals.dart';
import '../repositories/food_entry_repository.dart';
import '../repositories/nutritional_goals_repository.dart';
import '../../core/utils/result.dart';
import '../../core/utils/date_utils.dart';
import '../../core/errors/failures.dart';

class GetDailyNutritionUseCase {
  final FoodEntryRepository _foodEntryRepository;
  final NutritionalGoalsRepository _nutritionalGoalsRepository;

  GetDailyNutritionUseCase(
    this._foodEntryRepository,
    this._nutritionalGoalsRepository,
  );

  Future<Result<DailyNutritionData>> execute(GetDailyNutritionParams params) async {
    try {
      // Get food entries for the date
      final entriesResult = await _foodEntryRepository.getFoodEntriesByDate(
        params.userId,
        params.date,
      );

      if (entriesResult.isFailure) {
        return ResultFailure(entriesResult.failureValue!);
      }

      final foodEntries = entriesResult.successValue!;

      // Get nutritional goals
      final goalsResult = await _nutritionalGoalsRepository.getNutritionalGoals(params.userId);
      if (goalsResult.isFailure) {
        return ResultFailure(goalsResult.failureValue!);
      }

      final goals = goalsResult.successValue;

      // Create daily log
      final dailyLog = DailyLog(
        id: '${params.userId}_${AppDateUtils.formatDateAsString(params.date)}',
        userId: params.userId,
        date: params.date,
        foodEntries: foodEntries,
        totalNutrition: DailyLog.calculateTotalNutritionFromEntries(foodEntries),
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // Calculate progress percentages
      final progressData = goals != null 
          ? _calculateProgress(dailyLog.totalNutrition, goals)
          : <String, double>{};

      // Group entries by meal type
      final mealGroups = <String, List<FoodEntry>>{
        'breakfast': dailyLog.getFoodEntriesByMealType('breakfast'),
        'lunch': dailyLog.getFoodEntriesByMealType('lunch'),
        'dinner': dailyLog.getFoodEntriesByMealType('dinner'),
        'snack': dailyLog.getFoodEntriesByMealType('snack'),
      };

      final result = DailyNutritionData(
        dailyLog: dailyLog,
        nutritionalGoals: goals,
        progressPercentages: progressData,
        mealGroups: mealGroups,
      );

      return Success(result);
    } catch (e) {
      return ResultFailure(DatabaseFailure(message: 'Failed to get daily nutrition: $e'));
    }
  }

  Map<String, double> _calculateProgress(dynamic totalNutrition, NutritionalGoals goals) {
    return {
      'calories': (totalNutrition.calories / goals.dailyCalories) * 100,
      'protein': (totalNutrition.protein / goals.dailyProtein) * 100,
      'carbs': (totalNutrition.carbs / goals.dailyCarbs) * 100,
      'fats': (totalNutrition.fats / goals.dailyFats) * 100,
      'fiber': (totalNutrition.fiber / goals.dailyFiber) * 100,
      'sugar': (totalNutrition.sugar / goals.dailySugar) * 100,
      'sodium': (totalNutrition.sodium / goals.dailySodium) * 100,
    };
  }
}

class GetDailyNutritionParams {
  final String userId;
  final DateTime date;

  const GetDailyNutritionParams({
    required this.userId,
    required this.date,
  });
}

class DailyNutritionData {
  final DailyLog dailyLog;
  final NutritionalGoals? nutritionalGoals;
  final Map<String, double> progressPercentages;
  final Map<String, List<FoodEntry>> mealGroups;

  const DailyNutritionData({
    required this.dailyLog,
    this.nutritionalGoals,
    required this.progressPercentages,
    required this.mealGroups,
  });
}
