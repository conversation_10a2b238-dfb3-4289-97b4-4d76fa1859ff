import '../constants/app_constants.dart';

class NutritionCalculator {
  /// Calculate Basal Metabolic Rate (BMR) using Mifflin-St Jeor Equation
  static double calculateBMR({
    required double weight, // in kg
    required double height, // in cm
    required int age,
    required String gender, // 'male' or 'female'
  }) {
    if (gender.toLowerCase() == 'male') {
      return AppConstants.maleBmrConstant +
          (AppConstants.maleBmrWeightMultiplier * weight) +
          (AppConstants.maleBmrHeightMultiplier * height) -
          (AppConstants.maleBmrAgeMultiplier * age);
    } else {
      return AppConstants.femaleBmrConstant +
          (AppConstants.femaleBmrWeightMultiplier * weight) +
          (AppConstants.femaleBmrHeightMultiplier * height) -
          (AppConstants.femaleBmrAgeMultiplier * age);
    }
  }
  
  /// Calculate Total Daily Energy Expenditure (TDEE)
  static double calculateTDEE({
    required double bmr,
    required String activityLevel,
  }) {
    final multiplier = AppConstants.activityMultipliers[activityLevel] ?? 1.2;
    return bmr * multiplier;
  }
  
  /// Calculate daily calorie needs
  static double calculateDailyCalories({
    required double weight,
    required double height,
    required int age,
    required String gender,
    required String activityLevel,
    String goal = 'maintain', // 'lose', 'maintain', 'gain'
  }) {
    final bmr = calculateBMR(
      weight: weight,
      height: height,
      age: age,
      gender: gender,
    );
    
    final tdee = calculateTDEE(
      bmr: bmr,
      activityLevel: activityLevel,
    );
    
    // Adjust based on goal
    switch (goal.toLowerCase()) {
      case 'lose':
        return tdee - 500; // 500 calorie deficit for ~1 lb/week loss
      case 'gain':
        return tdee + 500; // 500 calorie surplus for ~1 lb/week gain
      default:
        return tdee; // maintain weight
    }
  }
  
  /// Calculate protein needs (grams per day)
  static double calculateProteinNeeds({
    required double weight,
    required String activityLevel,
  }) {
    // Base protein needs: 0.8g per kg body weight
    double baseProtein = weight * 0.8;
    
    // Adjust based on activity level
    switch (activityLevel) {
      case 'very_active':
      case 'extremely_active':
        return weight * 1.6; // Higher protein for very active individuals
      case 'moderately_active':
        return weight * 1.2;
      default:
        return baseProtein;
    }
  }
  
  /// Calculate carbohydrate needs (grams per day)
  static double calculateCarbNeeds({
    required double dailyCalories,
  }) {
    // 45-65% of calories from carbs, using 50% as default
    const carbPercentage = 0.50;
    const caloriesPerGramCarb = 4;
    
    return (dailyCalories * carbPercentage) / caloriesPerGramCarb;
  }
  
  /// Calculate fat needs (grams per day)
  static double calculateFatNeeds({
    required double dailyCalories,
  }) {
    // 20-35% of calories from fats, using 25% as default
    const fatPercentage = 0.25;
    const caloriesPerGramFat = 9;
    
    return (dailyCalories * fatPercentage) / caloriesPerGramFat;
  }
  
  /// Calculate fiber needs (grams per day)
  static double calculateFiberNeeds({
    required int age,
    required String gender,
  }) {
    if (gender.toLowerCase() == 'male') {
      return age <= 50 ? 38.0 : 30.0;
    } else {
      return age <= 50 ? 25.0 : 21.0;
    }
  }
  
  /// Calculate percentage of daily goal achieved
  static double calculatePercentage({
    required double current,
    required double goal,
  }) {
    if (goal == 0) return 0;
    return (current / goal) * 100;
  }
  
  /// Calculate remaining amount to reach goal
  static double calculateRemaining({
    required double current,
    required double goal,
  }) {
    final remaining = goal - current;
    return remaining > 0 ? remaining : 0;
  }
  
  /// Convert weight from pounds to kilograms
  static double poundsToKg(double pounds) {
    return pounds * 0.453592;
  }
  
  /// Convert weight from kilograms to pounds
  static double kgToPounds(double kg) {
    return kg * 2.20462;
  }
  
  /// Convert height from feet/inches to centimeters
  static double feetInchesToCm(int feet, int inches) {
    final totalInches = (feet * 12) + inches;
    return totalInches * 2.54;
  }
  
  /// Convert height from centimeters to feet/inches
  static Map<String, int> cmToFeetInches(double cm) {
    final totalInches = cm / 2.54;
    final feet = totalInches ~/ 12;
    final inches = (totalInches % 12).round();
    return {'feet': feet, 'inches': inches};
  }
}
