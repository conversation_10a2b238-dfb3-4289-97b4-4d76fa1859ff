class AppConstants {
  // App Information
  static const String appName = 'NutriAI';
  static const String appVersion = '1.0.0';

  // Database
  static const String databaseName = 'nutri_ai.db';
  static const int databaseVersion = 1;

  // API
  static const String geminiApiBaseUrl =
      'https://generativelanguage.googleapis.com/v1beta';
  static const int apiTimeoutSeconds = 30;

  // TODO: Replace with your actual Gemini API key
  static const String geminiApiKey = 'AIzaSyBlCYRlZSUqRzPh_tiRnP8uMbfHs_5G0Ik';

  // Shared Preferences Keys
  static const String keyIsFirstLaunch = 'is_first_launch';
  static const String keyUserId = 'user_id';
  static const String keyUserProfile = 'user_profile';
  static const String keyGeminiApiKey = 'gemini_api_key';

  // Default Nutritional Values (per day)
  static const double defaultCaloriesPerDay = 2000.0;
  static const double defaultProteinPerDay = 150.0; // grams
  static const double defaultCarbsPerDay = 250.0; // grams
  static const double defaultFatsPerDay = 65.0; // grams
  static const double defaultFiberPerDay = 25.0; // grams
  static const double defaultSugarPerDay = 50.0; // grams
  static const double defaultSodiumPerDay = 2300.0; // mg

  // Activity Level Multipliers
  static const Map<String, double> activityMultipliers = {
    'sedentary': 1.2,
    'lightly_active': 1.375,
    'moderately_active': 1.55,
    'very_active': 1.725,
    'extremely_active': 1.9,
  };

  // BMR Calculation Constants
  static const double maleBmrConstant = 88.362;
  static const double maleBmrWeightMultiplier = 13.397;
  static const double maleBmrHeightMultiplier = 4.799;
  static const double maleBmrAgeMultiplier = 5.677;

  static const double femaleBmrConstant = 447.593;
  static const double femaleBmrWeightMultiplier = 9.247;
  static const double femaleBmrHeightMultiplier = 3.098;
  static const double femaleBmrAgeMultiplier = 4.330;

  // Image Processing
  static const int maxImageSizeBytes = 5 * 1024 * 1024; // 5MB
  static const double imageCompressionQuality = 0.8;

  // UI Constants
  static const double defaultPadding = 16.0;
  static const double smallPadding = 8.0;
  static const double largePadding = 24.0;
  static const double borderRadius = 12.0;
  static const double cardElevation = 4.0;

  // Animation Durations
  static const Duration shortAnimationDuration = Duration(milliseconds: 200);
  static const Duration mediumAnimationDuration = Duration(milliseconds: 400);
  static const Duration longAnimationDuration = Duration(milliseconds: 600);

  // Date Formats
  static const String dateFormat = 'yyyy-MM-dd';
  static const String timeFormat = 'HH:mm';
  static const String dateTimeFormat = 'yyyy-MM-dd HH:mm:ss';
  static const String displayDateFormat = 'MMM dd, yyyy';

  // Error Messages
  static const String genericErrorMessage =
      'Something went wrong. Please try again.';
  static const String networkErrorMessage =
      'Please check your internet connection.';
  static const String cameraPermissionError =
      'Camera permission is required to take photos.';
  static const String storagePermissionError =
      'Storage permission is required to save images.';
  static const String invalidImageError = 'Please select a valid image file.';
  static const String apiKeyMissingError = 'Gemini API key is not configured.';

  // Success Messages
  static const String profileSavedMessage = 'Profile saved successfully!';
  static const String goalUpdatedMessage = 'Goals updated successfully!';
  static const String foodLoggedMessage = 'Food logged successfully!';
  static const String dataExportedMessage = 'Data exported successfully!';
}
