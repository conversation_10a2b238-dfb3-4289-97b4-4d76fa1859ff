import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;

import 'core/constants/app_constants.dart';
import 'core/routes/route_generator.dart';
import 'core/routes/app_routes.dart';
import 'data/datasources/database_helper.dart';
import 'data/datasources/gemini_api_service.dart';
import 'data/repositories/user_profile_repository_impl.dart';
import 'data/repositories/food_entry_repository_impl.dart';
import 'data/repositories/nutritional_goals_repository_impl.dart';
import 'domain/usecases/create_user_profile_usecase.dart';
import 'domain/usecases/analyze_food_image_usecase.dart';
import 'domain/usecases/get_daily_nutrition_usecase.dart';
import 'presentation/providers/app_provider.dart';
import 'presentation/providers/nutrition_provider.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize dependencies
  final sharedPreferences = await SharedPreferences.getInstance();
  final databaseHelper = DatabaseHelper();
  final httpClient = http.Client();

  // Initialize repositories
  final userProfileRepository = UserProfileRepositoryImpl(databaseHelper);
  final foodEntryRepository = FoodEntryRepositoryImpl(databaseHelper);
  final nutritionalGoalsRepository = NutritionalGoalsRepositoryImpl(
    databaseHelper,
  );

  // Initialize Gemini API service
  final geminiApiService = GeminiApiService(
    client: httpClient,
    apiKey: AppConstants.geminiApiKey,
  );

  // Initialize use cases
  final createUserProfileUseCase = CreateUserProfileUseCase(
    userProfileRepository,
    nutritionalGoalsRepository,
  );

  final getDailyNutritionUseCase = GetDailyNutritionUseCase(
    foodEntryRepository,
    nutritionalGoalsRepository,
  );

  final analyzeFoodImageUseCase = AnalyzeFoodImageUseCase(geminiApiService);

  runApp(
    NutriAIApp(
      sharedPreferences: sharedPreferences,
      userProfileRepository: userProfileRepository,
      foodEntryRepository: foodEntryRepository,
      nutritionalGoalsRepository: nutritionalGoalsRepository,
      createUserProfileUseCase: createUserProfileUseCase,
      getDailyNutritionUseCase: getDailyNutritionUseCase,
      analyzeFoodImageUseCase: analyzeFoodImageUseCase,
      httpClient: httpClient,
    ),
  );
}

class NutriAIApp extends StatelessWidget {
  final SharedPreferences sharedPreferences;
  final UserProfileRepositoryImpl userProfileRepository;
  final FoodEntryRepositoryImpl foodEntryRepository;
  final NutritionalGoalsRepositoryImpl nutritionalGoalsRepository;
  final CreateUserProfileUseCase createUserProfileUseCase;
  final GetDailyNutritionUseCase getDailyNutritionUseCase;
  final AnalyzeFoodImageUseCase analyzeFoodImageUseCase;
  final http.Client httpClient;

  const NutriAIApp({
    super.key,
    required this.sharedPreferences,
    required this.userProfileRepository,
    required this.foodEntryRepository,
    required this.nutritionalGoalsRepository,
    required this.createUserProfileUseCase,
    required this.getDailyNutritionUseCase,
    required this.analyzeFoodImageUseCase,
    required this.httpClient,
  });

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(
          create: (_) => AppProvider(userProfileRepository, sharedPreferences),
        ),
        ChangeNotifierProvider(
          create: (_) =>
              NutritionProvider(getDailyNutritionUseCase, foodEntryRepository),
        ),
        Provider<CreateUserProfileUseCase>.value(
          value: createUserProfileUseCase,
        ),
        Provider<AnalyzeFoodImageUseCase>.value(value: analyzeFoodImageUseCase),
      ],
      child: MaterialApp(
        title: AppConstants.appName,
        theme: _buildTheme(),
        initialRoute: AppRoutes.splash,
        onGenerateRoute: RouteGenerator.generateRoute,
        debugShowCheckedModeBanner: false,
      ),
    );
  }

  ThemeData _buildTheme() {
    const seedColor = Color(0xFF2E7D32); // Deep green for nutrition

    return ThemeData(
      useMaterial3: true,
      colorScheme: ColorScheme.fromSeed(
        seedColor: seedColor,
        brightness: Brightness.light,
      ),

      // Typography
      textTheme: const TextTheme(
        displayLarge: TextStyle(
          fontSize: 57,
          fontWeight: FontWeight.w400,
          letterSpacing: -0.25,
        ),
        displayMedium: TextStyle(fontSize: 45, fontWeight: FontWeight.w400),
        displaySmall: TextStyle(fontSize: 36, fontWeight: FontWeight.w400),
        headlineLarge: TextStyle(fontSize: 32, fontWeight: FontWeight.w600),
        headlineMedium: TextStyle(fontSize: 28, fontWeight: FontWeight.w600),
        headlineSmall: TextStyle(fontSize: 24, fontWeight: FontWeight.w600),
        titleLarge: TextStyle(fontSize: 22, fontWeight: FontWeight.w600),
        titleMedium: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w500,
          letterSpacing: 0.15,
        ),
        titleSmall: TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w500,
          letterSpacing: 0.1,
        ),
        bodyLarge: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w400,
          letterSpacing: 0.5,
        ),
        bodyMedium: TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w400,
          letterSpacing: 0.25,
        ),
        bodySmall: TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.w400,
          letterSpacing: 0.4,
        ),
        labelLarge: TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w500,
          letterSpacing: 0.1,
        ),
        labelMedium: TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.w500,
          letterSpacing: 0.5,
        ),
        labelSmall: TextStyle(
          fontSize: 11,
          fontWeight: FontWeight.w500,
          letterSpacing: 0.5,
        ),
      ),

      // App Bar Theme
      appBarTheme: AppBarTheme(
        centerTitle: false,
        elevation: 0,
        scrolledUnderElevation: 1,
        backgroundColor: Colors.transparent,
        surfaceTintColor: Colors.transparent,
        titleTextStyle: const TextStyle(
          fontSize: 22,
          fontWeight: FontWeight.w600,
          color: Color(0xFF1C1B1F),
        ),
        iconTheme: const IconThemeData(color: Color(0xFF49454F)),
      ),

      // Card Theme
      cardTheme: CardThemeData(
        elevation: 1,
        shadowColor: Colors.black.withValues(alpha: 0.05),
        surfaceTintColor: const Color(0xFFF7F2FA),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        margin: const EdgeInsets.symmetric(vertical: 4),
      ),

      // Button Themes
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          elevation: 1,
          shadowColor: Colors.black.withValues(alpha: 0.1),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        ),
      ),

      filledButtonTheme: FilledButtonThemeData(
        style: FilledButton.styleFrom(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        ),
      ),

      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          side: const BorderSide(width: 1),
        ),
      ),

      // Input Decoration Theme
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: const Color(0xFFF7F2FA),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide.none,
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide.none,
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: Color(0xFF2E7D32), width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: Color(0xFFBA1A1A), width: 1),
        ),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 16,
        ),
        hintStyle: const TextStyle(
          color: Color(0xFF79747E),
          fontSize: 16,
          fontWeight: FontWeight.w400,
        ),
        labelStyle: const TextStyle(
          color: Color(0xFF49454F),
          fontSize: 16,
          fontWeight: FontWeight.w400,
        ),
      ),

      // Navigation Bar Theme
      navigationBarTheme: NavigationBarThemeData(
        height: 80,
        elevation: 3,
        shadowColor: Colors.black.withValues(alpha: 0.1),
        backgroundColor: const Color(0xFFFFFBFE),
        indicatorColor: const Color(0xFFE8F5E8),
        labelTextStyle: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w500,
              color: Color(0xFF1D1B20),
            );
          }
          return const TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w500,
            color: Color(0xFF49454F),
          );
        }),
        iconTheme: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return const IconThemeData(color: Color(0xFF1D1B20), size: 24);
          }
          return const IconThemeData(color: Color(0xFF49454F), size: 24);
        }),
      ),

      // Divider Theme
      dividerTheme: const DividerThemeData(
        color: Color(0xFFE7E0EC),
        thickness: 1,
        space: 1,
      ),

      // List Tile Theme
      listTileTheme: const ListTileThemeData(
        contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 4),
        minLeadingWidth: 24,
        iconColor: Color(0xFF49454F),
        textColor: Color(0xFF1C1B1F),
      ),

      // Chip Theme
      chipTheme: ChipThemeData(
        backgroundColor: const Color(0xFFE7E0EC),
        selectedColor: const Color(0xFFE8F5E8),
        disabledColor: const Color(0xFFE7E0EC).withValues(alpha: 0.12),
        labelStyle: const TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w500,
          color: Color(0xFF1C1B1F),
        ),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      ),
    );
  }
}
