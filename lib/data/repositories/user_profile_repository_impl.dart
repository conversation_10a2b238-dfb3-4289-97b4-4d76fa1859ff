import 'package:sqflite/sqflite.dart';
import '../../domain/entities/user_profile.dart';
import '../../domain/repositories/user_profile_repository.dart';
import '../../core/utils/result.dart';
import '../../core/errors/failures.dart';
import '../datasources/database_helper.dart';
import '../models/user_profile_model.dart';

class UserProfileRepositoryImpl implements UserProfileRepository {
  final DatabaseHelper _databaseHelper;

  UserProfileRepositoryImpl(this._databaseHelper);

  @override
  Future<Result<UserProfile>> createUserProfile(UserProfile profile) async {
    try {
      final db = await _databaseHelper.database;
      final model = UserProfileModel.fromEntity(profile);

      await db.insert(
        'user_profiles',
        model.toDatabase(),
        conflictAlgorithm: ConflictAlgorithm.replace,
      );

      return Success(profile);
    } catch (e) {
      return ResultFailure(
        DatabaseFailure(message: 'Failed to create user profile: $e'),
      );
    }
  }

  @override
  Future<Result<UserProfile?>> getUserProfile(String userId) async {
    try {
      final db = await _databaseHelper.database;

      final List<Map<String, dynamic>> maps = await db.query(
        'user_profiles',
        where: 'id = ?',
        whereArgs: [userId],
      );

      if (maps.isEmpty) {
        return const Success(null);
      }

      final model = UserProfileModel.fromDatabase(maps.first);
      return Success(model);
    } catch (e) {
      return ResultFailure(
        DatabaseFailure(message: 'Failed to get user profile: $e'),
      );
    }
  }

  @override
  Future<Result<UserProfile>> updateUserProfile(UserProfile profile) async {
    try {
      final db = await _databaseHelper.database;
      final model = UserProfileModel.fromEntity(profile);

      final count = await db.update(
        'user_profiles',
        model.toDatabase(),
        where: 'id = ?',
        whereArgs: [profile.id],
      );

      if (count == 0) {
        return ResultFailure(
          DatabaseFailure(message: 'User profile not found'),
        );
      }

      return Success(profile);
    } catch (e) {
      return ResultFailure(
        DatabaseFailure(message: 'Failed to update user profile: $e'),
      );
    }
  }

  @override
  Future<Result<void>> deleteUserProfile(String userId) async {
    try {
      final db = await _databaseHelper.database;

      final count = await db.delete(
        'user_profiles',
        where: 'id = ?',
        whereArgs: [userId],
      );

      if (count == 0) {
        return ResultFailure(
          DatabaseFailure(message: 'User profile not found'),
        );
      }

      return const Success(null);
    } catch (e) {
      return ResultFailure(
        DatabaseFailure(message: 'Failed to delete user profile: $e'),
      );
    }
  }

  @override
  Future<Result<bool>> hasUserProfile() async {
    try {
      final db = await _databaseHelper.database;

      final List<Map<String, dynamic>> maps = await db.query(
        'user_profiles',
        columns: ['COUNT(*) as count'],
      );

      final count = maps.first['count'] as int;
      return Success(count > 0);
    } catch (e) {
      return ResultFailure(
        DatabaseFailure(message: 'Failed to check user profile existence: $e'),
      );
    }
  }

  @override
  Future<Result<UserProfile?>> getCurrentUserProfile() async {
    try {
      final db = await _databaseHelper.database;

      final List<Map<String, dynamic>> maps = await db.query(
        'user_profiles',
        orderBy: 'created_at DESC',
        limit: 1,
      );

      if (maps.isEmpty) {
        return const Success(null);
      }

      final model = UserProfileModel.fromDatabase(maps.first);
      return Success(model);
    } catch (e) {
      return ResultFailure(
        DatabaseFailure(message: 'Failed to get current user profile: $e'),
      );
    }
  }
}
