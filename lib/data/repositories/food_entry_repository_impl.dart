import 'package:sqflite/sqflite.dart';
import '../../domain/entities/food_entry.dart';
import '../../domain/repositories/food_entry_repository.dart';
import '../../core/utils/result.dart';
import '../../core/errors/failures.dart';
import '../../core/utils/date_utils.dart';
import '../datasources/database_helper.dart';
import '../models/food_entry_model.dart';

class FoodEntryRepositoryImpl implements FoodEntryRepository {
  final DatabaseHelper _databaseHelper;

  FoodEntryRepositoryImpl(this._databaseHelper);

  @override
  Future<Result<FoodEntry>> createFoodEntry(FoodEntry entry) async {
    try {
      final db = await _databaseHelper.database;
      final model = FoodEntryModel.fromEntity(entry);

      await db.insert(
        'food_entries',
        model.toDatabase(),
        conflictAlgorithm: ConflictAlgorithm.replace,
      );

      return Success(entry);
    } catch (e) {
      return ResultFailure(
        DatabaseFailure(message: 'Failed to create food entry: $e'),
      );
    }
  }

  @override
  Future<Result<FoodEntry?>> getFoodEntry(String entryId) async {
    try {
      final db = await _databaseHelper.database;

      final List<Map<String, dynamic>> maps = await db.query(
        'food_entries',
        where: 'id = ?',
        whereArgs: [entryId],
      );

      if (maps.isEmpty) {
        return const Success(null);
      }

      final model = FoodEntryModel.fromDatabase(maps.first);
      return Success(model);
    } catch (e) {
      return ResultFailure(
        DatabaseFailure(message: 'Failed to get food entry: $e'),
      );
    }
  }

  @override
  Future<Result<List<FoodEntry>>> getFoodEntriesByUser(String userId) async {
    try {
      final db = await _databaseHelper.database;

      final List<Map<String, dynamic>> maps = await db.query(
        'food_entries',
        where: 'user_id = ?',
        whereArgs: [userId],
        orderBy: 'consumed_at DESC',
      );

      final entries = maps
          .map((map) => FoodEntryModel.fromDatabase(map) as FoodEntry)
          .toList();
      return Success(entries);
    } catch (e) {
      return ResultFailure(
        DatabaseFailure(message: 'Failed to get food entries: $e'),
      );
    }
  }

  @override
  Future<Result<List<FoodEntry>>> getFoodEntriesByDate(
    String userId,
    DateTime date,
  ) async {
    try {
      final db = await _databaseHelper.database;
      final startOfDay = AppDateUtils.getStartOfDay(date);
      final endOfDay = AppDateUtils.getEndOfDay(date);

      final List<Map<String, dynamic>> maps = await db.query(
        'food_entries',
        where: 'user_id = ? AND consumed_at >= ? AND consumed_at <= ?',
        whereArgs: [
          userId,
          startOfDay.millisecondsSinceEpoch,
          endOfDay.millisecondsSinceEpoch,
        ],
        orderBy: 'consumed_at ASC',
      );

      final entries = maps
          .map((map) => FoodEntryModel.fromDatabase(map) as FoodEntry)
          .toList();
      return Success(entries);
    } catch (e) {
      return ResultFailure(
        DatabaseFailure(message: 'Failed to get food entries by date: $e'),
      );
    }
  }

  @override
  Future<Result<List<FoodEntry>>> getFoodEntriesByDateRange(
    String userId,
    DateTime startDate,
    DateTime endDate,
  ) async {
    try {
      final db = await _databaseHelper.database;
      final start = AppDateUtils.getStartOfDay(startDate);
      final end = AppDateUtils.getEndOfDay(endDate);

      final List<Map<String, dynamic>> maps = await db.query(
        'food_entries',
        where: 'user_id = ? AND consumed_at >= ? AND consumed_at <= ?',
        whereArgs: [
          userId,
          start.millisecondsSinceEpoch,
          end.millisecondsSinceEpoch,
        ],
        orderBy: 'consumed_at DESC',
      );

      final entries = maps
          .map((map) => FoodEntryModel.fromDatabase(map) as FoodEntry)
          .toList();
      return Success(entries);
    } catch (e) {
      return ResultFailure(
        DatabaseFailure(
          message: 'Failed to get food entries by date range: $e',
        ),
      );
    }
  }

  @override
  Future<Result<List<FoodEntry>>> getFoodEntriesByMealType(
    String userId,
    DateTime date,
    String mealType,
  ) async {
    try {
      final db = await _databaseHelper.database;
      final startOfDay = AppDateUtils.getStartOfDay(date);
      final endOfDay = AppDateUtils.getEndOfDay(date);

      final List<Map<String, dynamic>> maps = await db.query(
        'food_entries',
        where:
            'user_id = ? AND consumed_at >= ? AND consumed_at <= ? AND meal_type = ?',
        whereArgs: [
          userId,
          startOfDay.millisecondsSinceEpoch,
          endOfDay.millisecondsSinceEpoch,
          mealType,
        ],
        orderBy: 'consumed_at ASC',
      );

      final entries = maps
          .map((map) => FoodEntryModel.fromDatabase(map) as FoodEntry)
          .toList();
      return Success(entries);
    } catch (e) {
      return ResultFailure(
        DatabaseFailure(message: 'Failed to get food entries by meal type: $e'),
      );
    }
  }

  @override
  Future<Result<FoodEntry>> updateFoodEntry(FoodEntry entry) async {
    try {
      final db = await _databaseHelper.database;
      final model = FoodEntryModel.fromEntity(entry);

      final count = await db.update(
        'food_entries',
        model.toDatabase(),
        where: 'id = ?',
        whereArgs: [entry.id],
      );

      if (count == 0) {
        return ResultFailure(DatabaseFailure(message: 'Food entry not found'));
      }

      return Success(entry);
    } catch (e) {
      return ResultFailure(
        DatabaseFailure(message: 'Failed to update food entry: $e'),
      );
    }
  }

  @override
  Future<Result<void>> deleteFoodEntry(String entryId) async {
    try {
      final db = await _databaseHelper.database;

      final count = await db.delete(
        'food_entries',
        where: 'id = ?',
        whereArgs: [entryId],
      );

      if (count == 0) {
        return ResultFailure(DatabaseFailure(message: 'Food entry not found'));
      }

      return const Success(null);
    } catch (e) {
      return ResultFailure(
        DatabaseFailure(message: 'Failed to delete food entry: $e'),
      );
    }
  }

  @override
  Future<Result<List<FoodEntry>>> searchFoodEntries(
    String userId,
    String query,
  ) async {
    try {
      final db = await _databaseHelper.database;

      final List<Map<String, dynamic>> maps = await db.query(
        'food_entries',
        where:
            'user_id = ? AND (name LIKE ? OR description LIKE ? OR brand LIKE ?)',
        whereArgs: [userId, '%$query%', '%$query%', '%$query%'],
        orderBy: 'consumed_at DESC',
        limit: 50,
      );

      final entries = maps
          .map((map) => FoodEntryModel.fromDatabase(map) as FoodEntry)
          .toList();
      return Success(entries);
    } catch (e) {
      return ResultFailure(
        DatabaseFailure(message: 'Failed to search food entries: $e'),
      );
    }
  }
}
