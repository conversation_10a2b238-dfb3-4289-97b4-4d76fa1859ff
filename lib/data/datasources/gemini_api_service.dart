import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import '../../core/constants/app_constants.dart';
import '../../core/errors/failures.dart';
import '../../core/utils/result.dart';
import '../models/gemini_request_model.dart' as request;
import '../models/gemini_response_model.dart' as response;

class GeminiApiService {
  final http.Client _client;
  final String _apiKey;

  GeminiApiService({required http.Client client, required String apiKey})
    : _client = client,
      _apiKey = apiKey;

  Future<Result<response.FoodAnalysisResult>> analyzeFoodImage(
    File imageFile,
  ) async {
    try {
      // Read and encode image
      final imageBytes = await imageFile.readAsBytes();
      final base64Image = base64Encode(imageBytes);

      // Create request
      final requestModel = request.GeminiRequestModel(
        contents: [
          request.Content(
            parts: [
              request.Part.text(_getFoodAnalysisPrompt()),
              request.Part.image('image/jpeg', base64Image),
            ],
          ),
        ],
        generationConfig: const request.GenerationConfig(
          temperature: 0.1,
          topK: 32,
          topP: 1.0,
          maxOutputTokens: 4096,
        ),
        safetySettings: [
          const request.SafetySetting(
            category: 'HARM_CATEGORY_HARASSMENT',
            threshold: 'BLOCK_MEDIUM_AND_ABOVE',
          ),
          const request.SafetySetting(
            category: 'HARM_CATEGORY_HATE_SPEECH',
            threshold: 'BLOCK_MEDIUM_AND_ABOVE',
          ),
          const request.SafetySetting(
            category: 'HARM_CATEGORY_SEXUALLY_EXPLICIT',
            threshold: 'BLOCK_MEDIUM_AND_ABOVE',
          ),
          const request.SafetySetting(
            category: 'HARM_CATEGORY_DANGEROUS_CONTENT',
            threshold: 'BLOCK_MEDIUM_AND_ABOVE',
          ),
        ],
      );

      // Make API call
      final httpResponse = await _client
          .post(
            Uri.parse(
              '${AppConstants.geminiApiBaseUrl}/models/gemini-1.5-flash:generateContent?key=$_apiKey',
            ),
            headers: {'Content-Type': 'application/json'},
            body: jsonEncode(requestModel.toJson()),
          )
          .timeout(Duration(seconds: AppConstants.apiTimeoutSeconds));

      if (httpResponse.statusCode == 200) {
        final responseData =
            jsonDecode(httpResponse.body) as Map<String, dynamic>;
        final geminiResponse = response.GeminiResponseModel.fromJson(
          responseData,
        );

        if (geminiResponse.candidates.isNotEmpty) {
          final content = geminiResponse.candidates.first.content;
          if (content.parts.isNotEmpty && content.parts.first.text != null) {
            final analysisText = content.parts.first.text!;
            return _parseAnalysisResult(analysisText);
          }
        }

        return const ResultFailure(
          ApiFailure(message: 'No analysis result received'),
        );
      } else {
        final errorData = jsonDecode(httpResponse.body) as Map<String, dynamic>;
        final errorMessage =
            errorData['error']?['message'] ?? 'API request failed';
        return ResultFailure(
          ApiFailure(message: errorMessage, code: httpResponse.statusCode),
        );
      }
    } catch (e) {
      if (e is SocketException) {
        return const ResultFailure(
          NetworkFailure(message: 'No internet connection'),
        );
      }
      return ResultFailure(ApiFailure(message: 'Failed to analyze image: $e'));
    }
  }

  String _getFoodAnalysisPrompt() {
    return '''
Analyze this food image and provide detailed nutritional information in JSON format. 
Be as accurate as possible and provide your confidence level.

Please return ONLY a valid JSON object with this exact structure:
{
  "food_name": "Name of the food item",
  "description": "Brief description of the food",
  "brand": "Brand name if visible (null if not)",
  "serving_size": 100,
  "serving_unit": "grams",
  "nutrition": {
    "calories": 0.0,
    "protein": 0.0,
    "carbs": 0.0,
    "fats": 0.0,
    "fiber": 0.0,
    "sugar": 0.0,
    "sodium": 0.0,
    "vitaminA": 0.0,
    "vitaminC": 0.0,
    "vitaminD": 0.0,
    "calcium": 0.0,
    "iron": 0.0,
    "saturatedFat": 0.0,
    "transFat": 0.0,
    "cholesterol": 0.0,
    "potassium": 0.0
  },
  "confidence": 0.85,
  "ingredients": ["list", "of", "ingredients"],
  "category": "food category"
}

Guidelines:
- All nutrition values should be per serving
- Confidence should be between 0.0 and 1.0
- Use standard serving sizes (100g for most foods, 1 cup for liquids, 1 piece for items)
- If you can't identify the food, set confidence to 0.1 and provide best guess
- Nutrition values should be realistic and based on standard food databases
''';
  }

  Result<response.FoodAnalysisResult> _parseAnalysisResult(
    String analysisText,
  ) {
    try {
      // Clean the response text to extract JSON
      String jsonText = analysisText.trim();

      // Remove markdown code blocks if present
      if (jsonText.startsWith('```json')) {
        jsonText = jsonText.substring(7);
      }
      if (jsonText.startsWith('```')) {
        jsonText = jsonText.substring(3);
      }
      if (jsonText.endsWith('```')) {
        jsonText = jsonText.substring(0, jsonText.length - 3);
      }

      jsonText = jsonText.trim();

      // Validate that we have JSON content
      if (jsonText.isEmpty) {
        return const ResultFailure(
          ApiFailure(message: 'Empty response from AI analysis'),
        );
      }

      final jsonData = jsonDecode(jsonText) as Map<String, dynamic>;
      final result = response.FoodAnalysisResult.fromJson(jsonData);

      return Success(result);
    } catch (e) {
      return ResultFailure(
        ApiFailure(
          message:
              'Failed to parse analysis result: $e\nRaw response: $analysisText',
        ),
      );
    }
  }
}
