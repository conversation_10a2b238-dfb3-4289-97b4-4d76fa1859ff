import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import '../../core/constants/app_constants.dart';

class DatabaseHelper {
  static DatabaseHelper? _instance;
  static Database? _database;
  
  DatabaseHelper._internal();
  
  factory DatabaseHelper() {
    _instance ??= DatabaseHelper._internal();
    return _instance!;
  }
  
  Future<Database> get database async {
    _database ??= await _initDatabase();
    return _database!;
  }
  
  Future<Database> _initDatabase() async {
    final databasesPath = await getDatabasesPath();
    final path = join(databasesPath, AppConstants.databaseName);
    
    return await openDatabase(
      path,
      version: AppConstants.databaseVersion,
      onCreate: _onCreate,
      onUpgrade: _onUpgrade,
    );
  }
  
  Future<void> _onCreate(Database db, int version) async {
    await _createTables(db);
  }
  
  Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    // Handle database upgrades here
    if (oldVersion < newVersion) {
      // For now, just recreate tables
      await _dropTables(db);
      await _createTables(db);
    }
  }
  
  Future<void> _createTables(Database db) async {
    // User Profile table
    await db.execute('''
      CREATE TABLE user_profiles (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        age INTEGER NOT NULL,
        gender TEXT NOT NULL,
        weight REAL NOT NULL,
        height REAL NOT NULL,
        activity_level TEXT NOT NULL,
        goal TEXT NOT NULL,
        weight_unit TEXT NOT NULL,
        height_unit TEXT NOT NULL,
        created_at INTEGER NOT NULL,
        updated_at INTEGER NOT NULL
      )
    ''');
    
    // Nutritional Goals table
    await db.execute('''
      CREATE TABLE nutritional_goals (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        daily_calories REAL NOT NULL,
        daily_protein REAL NOT NULL,
        daily_carbs REAL NOT NULL,
        daily_fats REAL NOT NULL,
        daily_fiber REAL NOT NULL,
        daily_sugar REAL NOT NULL,
        daily_sodium REAL NOT NULL,
        daily_vitamin_a REAL NOT NULL,
        daily_vitamin_c REAL NOT NULL,
        daily_vitamin_d REAL NOT NULL,
        daily_calcium REAL NOT NULL,
        daily_iron REAL NOT NULL,
        created_at INTEGER NOT NULL,
        updated_at INTEGER NOT NULL,
        FOREIGN KEY (user_id) REFERENCES user_profiles (id) ON DELETE CASCADE
      )
    ''');
    
    // Food Entries table
    await db.execute('''
      CREATE TABLE food_entries (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        name TEXT NOT NULL,
        description TEXT,
        brand TEXT,
        serving_size REAL NOT NULL,
        serving_unit TEXT NOT NULL,
        nutrition_data TEXT NOT NULL,
        image_path TEXT,
        meal_type TEXT NOT NULL,
        consumed_at INTEGER NOT NULL,
        created_at INTEGER NOT NULL,
        updated_at INTEGER NOT NULL,
        notes TEXT,
        is_analyzed_by_ai INTEGER NOT NULL DEFAULT 0,
        confidence REAL,
        FOREIGN KEY (user_id) REFERENCES user_profiles (id) ON DELETE CASCADE
      )
    ''');
    
    // Daily Logs table
    await db.execute('''
      CREATE TABLE daily_logs (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        date TEXT NOT NULL,
        total_nutrition TEXT NOT NULL,
        weight REAL,
        notes TEXT,
        created_at INTEGER NOT NULL,
        updated_at INTEGER NOT NULL,
        FOREIGN KEY (user_id) REFERENCES user_profiles (id) ON DELETE CASCADE,
        UNIQUE(user_id, date)
      )
    ''');
    
    // Create indexes for better performance
    await db.execute('CREATE INDEX idx_food_entries_user_id ON food_entries (user_id)');
    await db.execute('CREATE INDEX idx_food_entries_consumed_at ON food_entries (consumed_at)');
    await db.execute('CREATE INDEX idx_food_entries_meal_type ON food_entries (meal_type)');
    await db.execute('CREATE INDEX idx_daily_logs_user_id ON daily_logs (user_id)');
    await db.execute('CREATE INDEX idx_daily_logs_date ON daily_logs (date)');
    await db.execute('CREATE INDEX idx_nutritional_goals_user_id ON nutritional_goals (user_id)');
  }
  
  Future<void> _dropTables(Database db) async {
    await db.execute('DROP TABLE IF EXISTS daily_logs');
    await db.execute('DROP TABLE IF EXISTS food_entries');
    await db.execute('DROP TABLE IF EXISTS nutritional_goals');
    await db.execute('DROP TABLE IF EXISTS user_profiles');
  }
  
  Future<void> closeDatabase() async {
    final db = _database;
    if (db != null) {
      await db.close();
      _database = null;
    }
  }
  
  Future<void> deleteDatabase() async {
    final databasesPath = await getDatabasesPath();
    final path = join(databasesPath, AppConstants.databaseName);
    await databaseFactory.deleteDatabase(path);
    _database = null;
  }
}
