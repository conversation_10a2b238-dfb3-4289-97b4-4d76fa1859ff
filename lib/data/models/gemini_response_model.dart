import '../../domain/entities/nutrition_data.dart';

class GeminiResponseModel {
  final List<Candidate> candidates;
  final PromptFeedback? promptFeedback;

  const GeminiResponseModel({required this.candidates, this.promptFeedback});

  factory GeminiResponseModel.fromJson(Map<String, dynamic> json) {
    return GeminiResponseModel(
      candidates: (json['candidates'] as List<dynamic>)
          .map(
            (candidate) =>
                Candidate.fromJson(candidate as Map<String, dynamic>),
          )
          .toList(),
      promptFeedback: json['promptFeedback'] != null
          ? PromptFeedback.fromJson(
              json['promptFeedback'] as Map<String, dynamic>,
            )
          : null,
    );
  }
}

class Candidate {
  final Content content;
  final String? finishReason;
  final int index;
  final List<SafetyRating>? safetyRatings;

  const Candidate({
    required this.content,
    this.finishReason,
    required this.index,
    this.safetyRatings,
  });

  factory Candidate.fromJson(Map<String, dynamic> json) {
    return Candidate(
      content: Content.fromJson(json['content'] as Map<String, dynamic>),
      finishReason: json['finishReason'] as String?,
      index: (json['index'] as num?)?.toInt() ?? 0,
      safetyRatings: json['safetyRatings'] != null
          ? (json['safetyRatings'] as List<dynamic>)
                .map(
                  (rating) =>
                      SafetyRating.fromJson(rating as Map<String, dynamic>),
                )
                .toList()
          : null,
    );
  }
}

class Content {
  final List<Part> parts;
  final String? role;

  const Content({required this.parts, this.role});

  factory Content.fromJson(Map<String, dynamic> json) {
    return Content(
      parts: (json['parts'] as List<dynamic>)
          .map((part) => Part.fromJson(part as Map<String, dynamic>))
          .toList(),
      role: json['role'] as String?,
    );
  }
}

class Part {
  final String? text;

  const Part({this.text});

  factory Part.fromJson(Map<String, dynamic> json) {
    return Part(text: json['text'] as String?);
  }
}

class SafetyRating {
  final String category;
  final String probability;

  const SafetyRating({required this.category, required this.probability});

  factory SafetyRating.fromJson(Map<String, dynamic> json) {
    return SafetyRating(
      category: json['category'] as String,
      probability: json['probability'] as String,
    );
  }
}

class PromptFeedback {
  final List<SafetyRating>? safetyRatings;

  const PromptFeedback({this.safetyRatings});

  factory PromptFeedback.fromJson(Map<String, dynamic> json) {
    return PromptFeedback(
      safetyRatings: json['safetyRatings'] != null
          ? (json['safetyRatings'] as List<dynamic>)
                .map(
                  (rating) =>
                      SafetyRating.fromJson(rating as Map<String, dynamic>),
                )
                .toList()
          : null,
    );
  }
}

class FoodAnalysisResult {
  final String foodName;
  final String? description;
  final String? brand;
  final double servingSize;
  final String servingUnit;
  final NutritionData nutritionData;
  final double confidence;
  final List<String>? ingredients;
  final String? category;

  const FoodAnalysisResult({
    required this.foodName,
    this.description,
    this.brand,
    required this.servingSize,
    required this.servingUnit,
    required this.nutritionData,
    required this.confidence,
    this.ingredients,
    this.category,
  });

  factory FoodAnalysisResult.fromJson(Map<String, dynamic> json) {
    return FoodAnalysisResult(
      foodName: json['food_name'] as String? ?? 'Unknown Food',
      description: json['description'] as String?,
      brand: json['brand'] as String?,
      servingSize: (json['serving_size'] as num?)?.toDouble() ?? 1.0,
      servingUnit: json['serving_unit'] as String? ?? 'serving',
      nutritionData: json['nutrition'] != null
          ? NutritionData.fromJson(json['nutrition'] as Map<String, dynamic>)
          : NutritionData.empty(),
      confidence: (json['confidence'] as num?)?.toDouble() ?? 0.5,
      ingredients: json['ingredients'] != null
          ? List<String>.from(json['ingredients'] as List<dynamic>)
          : null,
      category: json['category'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'food_name': foodName,
      'description': description,
      'brand': brand,
      'serving_size': servingSize,
      'serving_unit': servingUnit,
      'nutrition': nutritionData.toJson(),
      'confidence': confidence,
      'ingredients': ingredients,
      'category': category,
    };
  }
}
