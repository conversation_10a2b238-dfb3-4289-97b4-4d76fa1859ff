class GeminiRequestModel {
  final List<Content> contents;
  final GenerationConfig? generationConfig;
  final List<SafetySetting>? safetySettings;

  const GeminiRequestModel({
    required this.contents,
    this.generationConfig,
    this.safetySettings,
  });

  Map<String, dynamic> toJson() {
    return {
      'contents': contents.map((content) => content.toJson()).toList(),
      if (generationConfig != null) 'generationConfig': generationConfig!.toJson(),
      if (safetySettings != null) 'safetySettings': safetySettings!.map((setting) => setting.toJson()).toList(),
    };
  }
}

class Content {
  final List<Part> parts;
  final String? role;

  const Content({
    required this.parts,
    this.role,
  });

  Map<String, dynamic> toJson() {
    return {
      'parts': parts.map((part) => part.toJson()).toList(),
      if (role != null) 'role': role,
    };
  }
}

class Part {
  final String? text;
  final InlineData? inlineData;

  const Part({
    this.text,
    this.inlineData,
  });

  Part.text(String text) : this(text: text);
  Part.image(String mimeType, String data) : this(inlineData: InlineData(mimeType: mimeType, data: data));

  Map<String, dynamic> toJson() {
    return {
      if (text != null) 'text': text,
      if (inlineData != null) 'inline_data': inlineData!.toJson(),
    };
  }
}

class InlineData {
  final String mimeType;
  final String data;

  const InlineData({
    required this.mimeType,
    required this.data,
  });

  Map<String, dynamic> toJson() {
    return {
      'mime_type': mimeType,
      'data': data,
    };
  }
}

class GenerationConfig {
  final double? temperature;
  final int? topK;
  final double? topP;
  final int? maxOutputTokens;
  final List<String>? stopSequences;

  const GenerationConfig({
    this.temperature,
    this.topK,
    this.topP,
    this.maxOutputTokens,
    this.stopSequences,
  });

  Map<String, dynamic> toJson() {
    return {
      if (temperature != null) 'temperature': temperature,
      if (topK != null) 'topK': topK,
      if (topP != null) 'topP': topP,
      if (maxOutputTokens != null) 'maxOutputTokens': maxOutputTokens,
      if (stopSequences != null) 'stopSequences': stopSequences,
    };
  }
}

class SafetySetting {
  final String category;
  final String threshold;

  const SafetySetting({
    required this.category,
    required this.threshold,
  });

  Map<String, dynamic> toJson() {
    return {
      'category': category,
      'threshold': threshold,
    };
  }
}
