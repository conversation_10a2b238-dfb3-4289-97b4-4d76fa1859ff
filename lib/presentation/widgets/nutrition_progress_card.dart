import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../core/constants/app_constants.dart';
import '../../core/widgets/custom_card.dart';
import '../providers/nutrition_provider.dart';

class NutritionProgressCard extends StatelessWidget {
  const NutritionProgressCard({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<NutritionProvider>(
      builder: (context, nutritionProvider, child) {
        final currentData = nutritionProvider.currentDayData;
        final goals = nutritionProvider.nutritionalGoals;
        final progress = nutritionProvider.progressPercentages;
        
        if (currentData == null || goals == null) {
          return CustomCard(
            child: Column(
              children: [
                Text(
                  'Nutrition Progress',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: AppConstants.defaultPadding),
                const Text('No data available'),
              ],
            ),
          );
        }

        final totalNutrition = currentData.dailyLog.totalNutrition;

        return CustomCard(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Nutrition Progress',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              
              const SizedBox(height: AppConstants.defaultPadding),
              
              // Calories
              _buildProgressItem(
                context,
                'Calories',
                totalNutrition.calories,
                goals.dailyCalories,
                progress['calories'] ?? 0,
                'cal',
                Colors.orange,
              ),
              
              const SizedBox(height: AppConstants.defaultPadding),
              
              // Macronutrients
              Row(
                children: [
                  Expanded(
                    child: _buildMacroProgress(
                      context,
                      'Protein',
                      totalNutrition.protein,
                      goals.dailyProtein,
                      progress['protein'] ?? 0,
                      'g',
                      Colors.red,
                    ),
                  ),
                  
                  const SizedBox(width: AppConstants.smallPadding),
                  
                  Expanded(
                    child: _buildMacroProgress(
                      context,
                      'Carbs',
                      totalNutrition.carbs,
                      goals.dailyCarbs,
                      progress['carbs'] ?? 0,
                      'g',
                      Colors.blue,
                    ),
                  ),
                  
                  const SizedBox(width: AppConstants.smallPadding),
                  
                  Expanded(
                    child: _buildMacroProgress(
                      context,
                      'Fats',
                      totalNutrition.fats,
                      goals.dailyFats,
                      progress['fats'] ?? 0,
                      'g',
                      Colors.green,
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildProgressItem(
    BuildContext context,
    String label,
    double current,
    double goal,
    double percentage,
    String unit,
    Color color,
  ) {
    final theme = Theme.of(context);
    final progressValue = (percentage / 100).clamp(0.0, 1.0);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              label,
              style: theme.textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
            Text(
              '${current.toInt()} / ${goal.toInt()} $unit',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ),
          ],
        ),
        
        const SizedBox(height: AppConstants.smallPadding),
        
        LinearProgressIndicator(
          value: progressValue,
          backgroundColor: theme.colorScheme.surfaceVariant,
          valueColor: AlwaysStoppedAnimation<Color>(color),
          minHeight: 8,
        ),
        
        const SizedBox(height: 4),
        
        Text(
          '${percentage.toInt()}%',
          style: theme.textTheme.bodySmall?.copyWith(
            color: color,
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  Widget _buildMacroProgress(
    BuildContext context,
    String label,
    double current,
    double goal,
    double percentage,
    String unit,
    Color color,
  ) {
    final theme = Theme.of(context);
    final progressValue = (percentage / 100).clamp(0.0, 1.0);
    
    return Column(
      children: [
        SizedBox(
          width: 60,
          height: 60,
          child: Stack(
            children: [
              Center(
                child: SizedBox(
                  width: 50,
                  height: 50,
                  child: CircularProgressIndicator(
                    value: progressValue,
                    backgroundColor: theme.colorScheme.surfaceVariant,
                    valueColor: AlwaysStoppedAnimation<Color>(color),
                    strokeWidth: 4,
                  ),
                ),
              ),
              Center(
                child: Text(
                  '${percentage.toInt()}%',
                  style: theme.textTheme.labelSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
              ),
            ],
          ),
        ),
        
        const SizedBox(height: AppConstants.smallPadding),
        
        Text(
          label,
          style: theme.textTheme.bodySmall?.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
        
        Text(
          '${current.toInt()}g',
          style: theme.textTheme.bodySmall?.copyWith(
            color: theme.colorScheme.onSurfaceVariant,
          ),
        ),
      ],
    );
  }
}
