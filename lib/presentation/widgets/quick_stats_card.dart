import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../core/constants/app_constants.dart';
import '../../core/widgets/custom_card.dart';
import '../providers/nutrition_provider.dart';

class QuickStatsCard extends StatelessWidget {
  const QuickStatsCard({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<NutritionProvider>(
      builder: (context, nutritionProvider, child) {
        final currentData = nutritionProvider.currentDayData;
        final goals = nutritionProvider.nutritionalGoals;
        
        if (currentData == null || goals == null) {
          return const SizedBox.shrink();
        }

        final totalNutrition = currentData.dailyLog.totalNutrition;
        final remainingCalories = goals.dailyCalories - totalNutrition.calories;
        final totalEntries = currentData.dailyLog.foodEntries.length;

        return CustomCard(
          child: Row(
            children: [
              Expanded(
                child: _buildStatItem(
                  context,
                  'Remaining',
                  '${remainingCalories.toInt()}',
                  'calories',
                  Icons.local_fire_department,
                  remainingCalories >= 0 ? Colors.green : Colors.red,
                ),
              ),
              
              Container(
                width: 1,
                height: 40,
                color: Theme.of(context).colorScheme.outline.withOpacity(0.3),
              ),
              
              Expanded(
                child: _buildStatItem(
                  context,
                  'Entries',
                  totalEntries.toString(),
                  'today',
                  Icons.restaurant,
                  Theme.of(context).colorScheme.primary,
                ),
              ),
              
              Container(
                width: 1,
                height: 40,
                color: Theme.of(context).colorScheme.outline.withOpacity(0.3),
              ),
              
              Expanded(
                child: _buildStatItem(
                  context,
                  'Water',
                  '0',
                  'glasses',
                  Icons.water_drop,
                  Colors.blue,
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildStatItem(
    BuildContext context,
    String label,
    String value,
    String unit,
    IconData icon,
    Color color,
  ) {
    final theme = Theme.of(context);
    
    return Column(
      children: [
        Icon(
          icon,
          color: color,
          size: 24,
        ),
        
        const SizedBox(height: AppConstants.smallPadding),
        
        Text(
          value,
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        
        Text(
          label,
          style: theme.textTheme.bodySmall?.copyWith(
            color: theme.colorScheme.onSurfaceVariant,
          ),
        ),
        
        Text(
          unit,
          style: theme.textTheme.bodySmall?.copyWith(
            color: theme.colorScheme.onSurfaceVariant,
          ),
        ),
      ],
    );
  }
}
