import 'package:flutter/material.dart';
import '../../core/constants/app_constants.dart';
import '../../core/widgets/custom_button.dart';

class FoodAnalysisPage extends StatefulWidget {
  final String imagePath;
  final String mealType;

  const FoodAnalysisPage({
    super.key,
    required this.imagePath,
    required this.mealType,
  });

  @override
  State<FoodAnalysisPage> createState() => _FoodAnalysisPageState();
}

class _FoodAnalysisPageState extends State<FoodAnalysisPage> {
  bool _isAnalyzing = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Food Analysis'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Image preview
            Container(
              width: double.infinity,
              height: 200,
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surfaceVariant,
                borderRadius: BorderRadius.circular(AppConstants.borderRadius),
              ),
              child: Icon(
                Icons.image,
                size: 64,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
            
            const SizedBox(height: AppConstants.largePadding),
            
            Text(
              'AI Analysis Results',
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            
            const SizedBox(height: AppConstants.defaultPadding),
            
            if (_isAnalyzing) ...[
              const Center(
                child: Column(
                  children: [
                    CircularProgressIndicator(),
                    SizedBox(height: AppConstants.defaultPadding),
                    Text('Analyzing food image...'),
                  ],
                ),
              ),
            ] else ...[
              Text(
                'Food analysis functionality will be implemented here.\nThis will show the AI-detected food items and their nutritional information.',
                style: Theme.of(context).textTheme.bodyLarge,
              ),
            ],
            
            const Spacer(),
            
            Row(
              children: [
                Expanded(
                  child: CustomButton(
                    text: 'Analyze Again',
                    onPressed: () {
                      setState(() => _isAnalyzing = true);
                      // TODO: Implement analysis
                      Future.delayed(const Duration(seconds: 2), () {
                        if (mounted) {
                          setState(() => _isAnalyzing = false);
                        }
                      });
                    },
                    isOutlined: true,
                    icon: Icons.refresh,
                  ),
                ),
                
                const SizedBox(width: AppConstants.defaultPadding),
                
                Expanded(
                  child: CustomButton(
                    text: 'Save Entry',
                    onPressed: () {
                      // TODO: Save food entry
                      Navigator.pop(context);
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('Food entry saved!'),
                        ),
                      );
                    },
                    icon: Icons.save,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
