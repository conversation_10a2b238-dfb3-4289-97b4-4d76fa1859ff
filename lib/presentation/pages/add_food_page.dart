import 'package:flutter/material.dart';
import '../../core/constants/app_constants.dart';
import '../../core/widgets/custom_card.dart';
import '../../domain/entities/food_entry.dart';

class AddFoodPage extends StatelessWidget {
  final String mealType;
  final FoodEntry? foodEntry;

  const AddFoodPage({
    super.key,
    required this.mealType,
    this.foodEntry,
  });

  @override
  Widget build(BuildContext context) {
    final isEditing = foodEntry != null;
    
    return Scaffold(
      appBar: AppBar(
        title: Text(isEditing ? 'Edit Food' : 'Add Food'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          children: [
            CustomCard(
              child: Column(
                children: [
                  Icon(
                    Icons.restaurant,
                    size: 64,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                  const SizedBox(height: AppConstants.defaultPadding),
                  Text(
                    isEditing ? 'Edit Food Entry' : 'Manual Food Entry',
                    style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: AppConstants.defaultPadding),
                  Text(
                    isEditing 
                        ? 'Edit the details of your food entry.'
                        : 'Manually enter food details and nutrition information.',
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
