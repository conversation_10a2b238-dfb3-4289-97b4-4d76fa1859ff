import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:provider/provider.dart';
import 'dart:io';
import '../../core/constants/app_constants.dart';
import '../../core/widgets/custom_button.dart';
import '../../domain/usecases/analyze_food_image_usecase.dart';
import '../providers/nutrition_provider.dart';
import '../providers/app_provider.dart';

class CameraPage extends StatefulWidget {
  final String mealType;

  const CameraPage({super.key, required this.mealType});

  @override
  State<CameraPage> createState() => _CameraPageState();
}

class _CameraPageState extends State<CameraPage> {
  final ImagePicker _picker = ImagePicker();
  bool _isAnalyzing = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('Add ${widget.mealType.toUpperCase()}')),
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.camera_alt,
                size: 100,
                color: Theme.of(context).colorScheme.primary,
              ),

              const SizedBox(height: AppConstants.largePadding),

              Text(
                'Camera Feature',
                style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),

              const SizedBox(height: AppConstants.defaultPadding),

              Text(
                'Camera functionality will be implemented here.\nThis will include camera preview, photo capture, and image analysis.',
                textAlign: TextAlign.center,
                style: Theme.of(context).textTheme.bodyLarge,
              ),

              const SizedBox(height: AppConstants.largePadding),

              CustomButton(
                text: 'Take Photo',
                onPressed: _isAnalyzing ? null : _takePhoto,
                icon: Icons.camera_alt,
                isLoading: _isAnalyzing,
              ),

              const SizedBox(height: AppConstants.defaultPadding),

              CustomButton(
                text: 'Choose from Gallery',
                onPressed: _isAnalyzing ? null : _pickImageFromGallery,
                isOutlined: true,
                icon: Icons.photo_library,
                isLoading: _isAnalyzing,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _takePhoto() async {
    try {
      final XFile? photo = await _picker.pickImage(
        source: ImageSource.camera,
        maxWidth: 1024,
        maxHeight: 1024,
        imageQuality: 85,
      );

      if (photo != null) {
        await _analyzeImage(File(photo.path));
      }
    } catch (e) {
      _showError('Failed to take photo: $e');
    }
  }

  Future<void> _pickImageFromGallery() async {
    try {
      final XFile? image = await _picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1024,
        maxHeight: 1024,
        imageQuality: 85,
      );

      if (image != null) {
        await _analyzeImage(File(image.path));
      }
    } catch (e) {
      _showError('Failed to pick image from gallery: $e');
    }
  }

  Future<void> _analyzeImage(File imageFile) async {
    setState(() => _isAnalyzing = true);

    try {
      final appProvider = context.read<AppProvider>();
      final nutritionProvider = context.read<NutritionProvider>();
      final analyzeFoodImageUseCase = context.read<AnalyzeFoodImageUseCase>();

      if (appProvider.currentUser == null) {
        _showError('User not found');
        return;
      }

      final params = AnalyzeFoodImageParams(
        entryId: DateTime.now().millisecondsSinceEpoch.toString(),
        imageFile: imageFile,
        userId: appProvider.currentUser!.id,
        mealType: widget.mealType,
      );

      final result = await analyzeFoodImageUseCase.execute(params);

      if (result.isSuccess && result.successValue != null) {
        // Add the food entry to nutrition provider
        await nutritionProvider.addFoodEntry(result.successValue!);

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Food analyzed and added successfully!'),
              backgroundColor: Colors.green,
            ),
          );
          Navigator.of(context).pop();
        }
      } else {
        _showError(result.failureValue?.message ?? 'Failed to analyze image');
      }
    } catch (e) {
      _showError('An error occurred: $e');
    } finally {
      if (mounted) {
        setState(() => _isAnalyzing = false);
      }
    }
  }

  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Theme.of(context).colorScheme.error,
      ),
    );
  }
}
