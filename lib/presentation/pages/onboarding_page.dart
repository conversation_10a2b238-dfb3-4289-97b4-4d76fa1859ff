import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../core/constants/app_constants.dart';
import '../../core/routes/app_routes.dart';
import '../../core/widgets/custom_button.dart';
import '../../core/widgets/custom_card.dart';

import '../../domain/usecases/create_user_profile_usecase.dart';
import '../providers/app_provider.dart';

class OnboardingPage extends StatefulWidget {
  const OnboardingPage({super.key});

  @override
  State<OnboardingPage> createState() => _OnboardingPageState();
}

class _OnboardingPageState extends State<OnboardingPage> {
  final PageController _pageController = PageController();
  int _currentPage = 0;
  bool _isLoading = false;

  // Form controllers
  final _nameController = TextEditingController();
  final _ageController = TextEditingController();
  final _weightController = TextEditingController();
  final _heightController = TextEditingController();

  // Form values
  String _gender = 'male';
  String _activityLevel = 'moderately_active';
  String _goal = 'maintain';
  String _weightUnit = 'kg';
  String _heightUnit = 'cm';

  final _formKey = GlobalKey<FormState>();

  @override
  void dispose() {
    _pageController.dispose();
    _nameController.dispose();
    _ageController.dispose();
    _weightController.dispose();
    _heightController.dispose();
    super.dispose();
  }

  void _nextPage() {
    if (_currentPage < 2) {
      _pageController.nextPage(
        duration: AppConstants.mediumAnimationDuration,
        curve: Curves.easeInOut,
      );
    } else {
      _completeOnboarding();
    }
  }

  void _previousPage() {
    if (_currentPage > 0) {
      _pageController.previousPage(
        duration: AppConstants.mediumAnimationDuration,
        curve: Curves.easeInOut,
      );
    }
  }

  bool _validateFormFields() {
    // Validate name
    if (_nameController.text.trim().isEmpty) {
      _showError('Please enter your name');
      return false;
    }

    // Validate age
    final age = int.tryParse(_ageController.text);
    if (age == null || age < 13 || age > 120) {
      _showError('Please enter a valid age (13-120)');
      return false;
    }

    // Validate weight
    final weight = double.tryParse(_weightController.text);
    if (weight == null || weight <= 0) {
      _showError('Please enter a valid weight');
      return false;
    }

    // Validate height
    final height = double.tryParse(_heightController.text);
    if (height == null || height <= 0) {
      _showError('Please enter a valid height');
      return false;
    }

    return true;
  }

  Future<void> _completeOnboarding() async {
    // Validate form fields manually since the form key is only on page 1
    if (!_validateFormFields()) return;

    setState(() => _isLoading = true);

    try {
      final appProvider = context.read<AppProvider>();
      final createUserProfileUseCase = Provider.of<CreateUserProfileUseCase>(
        context,
        listen: false,
      );

      // API key is now hardcoded in constants
      final params = CreateUserProfileParams(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        name: _nameController.text.trim(),
        age: int.parse(_ageController.text),
        gender: _gender,
        weight: double.parse(_weightController.text),
        height: double.parse(_heightController.text),
        activityLevel: _activityLevel,
        goal: _goal,
        weightUnit: _weightUnit,
        heightUnit: _heightUnit,
      );

      final result = await createUserProfileUseCase.execute(params);

      if (result.isSuccess && result.successValue != null) {
        await appProvider.completeOnboarding(result.successValue!);

        if (mounted) {
          Navigator.of(context).pushReplacementNamed(AppRoutes.main);
        }
      } else {
        _showError(result.failureValue?.message ?? 'Failed to create profile');
      }
    } catch (e) {
      _showError('An unexpected error occurred: $e');
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Theme.of(context).colorScheme.error,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Column(
          children: [
            // Progress indicator
            LinearProgressIndicator(
              value: (_currentPage + 1) / 3,
              backgroundColor: Theme.of(
                context,
              ).colorScheme.surfaceContainerHighest,
            ),

            // Page content
            Expanded(
              child: PageView(
                controller: _pageController,
                onPageChanged: (page) => setState(() => _currentPage = page),
                children: [
                  _buildWelcomePage(),
                  _buildPersonalInfoPage(),
                  _buildGoalsPage(),
                ],
              ),
            ),

            // Navigation buttons
            _buildNavigationButtons(),
          ],
        ),
      ),
    );
  }

  Widget _buildWelcomePage() {
    final theme = Theme.of(context);

    return Padding(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.restaurant_menu,
            size: 100,
            color: theme.colorScheme.primary,
          ),

          const SizedBox(height: AppConstants.largePadding),

          Text(
            'Welcome to ${AppConstants.appName}',
            style: theme.textTheme.headlineMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: AppConstants.defaultPadding),

          Text(
            'Track your nutrition with AI-powered food analysis. '
            'Take photos of your meals and get detailed nutritional information instantly.',
            style: theme.textTheme.bodyLarge,
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: AppConstants.largePadding),

          CustomCard(
            child: Column(
              children: [
                _buildFeatureItem(
                  Icons.camera_alt,
                  'Photo Analysis',
                  'Analyze food photos with AI',
                ),
                const Divider(),
                _buildFeatureItem(
                  Icons.track_changes,
                  'Progress Tracking',
                  'Monitor your daily nutrition goals',
                ),
                const Divider(),
                _buildFeatureItem(
                  Icons.analytics,
                  'Insights',
                  'Get detailed nutrition analytics',
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFeatureItem(IconData icon, String title, String description) {
    final theme = Theme.of(context);

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: AppConstants.smallPadding),
      child: Row(
        children: [
          Icon(icon, color: theme.colorScheme.primary),
          const SizedBox(width: AppConstants.defaultPadding),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  description,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPersonalInfoPage() {
    return Padding(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Personal Information',
              style: Theme.of(
                context,
              ).textTheme.headlineMedium?.copyWith(fontWeight: FontWeight.bold),
            ),

            const SizedBox(height: AppConstants.defaultPadding),

            Text(
              'Help us personalize your nutrition goals',
              style: Theme.of(context).textTheme.bodyLarge,
            ),

            const SizedBox(height: AppConstants.largePadding),

            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    TextFormField(
                      controller: _nameController,
                      decoration: const InputDecoration(
                        labelText: 'Name',
                        prefixIcon: Icon(Icons.person),
                      ),
                      validator: (value) {
                        if (value?.trim().isEmpty ?? true) {
                          return 'Please enter your name';
                        }
                        return null;
                      },
                    ),

                    const SizedBox(height: AppConstants.defaultPadding),

                    Row(
                      children: [
                        Expanded(
                          child: TextFormField(
                            controller: _ageController,
                            decoration: const InputDecoration(
                              labelText: 'Age',
                              prefixIcon: Icon(Icons.cake),
                            ),
                            keyboardType: TextInputType.number,
                            validator: (value) {
                              final age = int.tryParse(value ?? '');
                              if (age == null || age < 13 || age > 120) {
                                return 'Enter valid age (13-120)';
                              }
                              return null;
                            },
                          ),
                        ),

                        const SizedBox(width: AppConstants.defaultPadding),

                        Expanded(
                          child: DropdownButtonFormField<String>(
                            value: _gender,
                            decoration: const InputDecoration(
                              labelText: 'Gender',
                              prefixIcon: Icon(Icons.wc),
                            ),
                            items: const [
                              DropdownMenuItem(
                                value: 'male',
                                child: Text('Male'),
                              ),
                              DropdownMenuItem(
                                value: 'female',
                                child: Text('Female'),
                              ),
                            ],
                            onChanged: (value) =>
                                setState(() => _gender = value!),
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: AppConstants.defaultPadding),

                    Row(
                      children: [
                        Expanded(
                          flex: 2,
                          child: TextFormField(
                            controller: _weightController,
                            decoration: const InputDecoration(
                              labelText: 'Weight',
                              prefixIcon: Icon(Icons.monitor_weight),
                            ),
                            keyboardType: TextInputType.number,
                            validator: (value) {
                              final weight = double.tryParse(value ?? '');
                              if (weight == null || weight <= 0) {
                                return 'Enter valid weight';
                              }
                              return null;
                            },
                          ),
                        ),

                        const SizedBox(width: AppConstants.smallPadding),

                        Expanded(
                          child: DropdownButtonFormField<String>(
                            value: _weightUnit,
                            items: const [
                              DropdownMenuItem(value: 'kg', child: Text('kg')),
                              DropdownMenuItem(
                                value: 'lbs',
                                child: Text('lbs'),
                              ),
                            ],
                            onChanged: (value) =>
                                setState(() => _weightUnit = value!),
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: AppConstants.defaultPadding),

                    Row(
                      children: [
                        Expanded(
                          flex: 2,
                          child: TextFormField(
                            controller: _heightController,
                            decoration: const InputDecoration(
                              labelText: 'Height',
                              prefixIcon: Icon(Icons.height),
                            ),
                            keyboardType: TextInputType.number,
                            validator: (value) {
                              final height = double.tryParse(value ?? '');
                              if (height == null || height <= 0) {
                                return 'Enter valid height';
                              }
                              return null;
                            },
                          ),
                        ),

                        const SizedBox(width: AppConstants.smallPadding),

                        Expanded(
                          child: DropdownButtonFormField<String>(
                            value: _heightUnit,
                            items: const [
                              DropdownMenuItem(value: 'cm', child: Text('cm')),
                              DropdownMenuItem(value: 'ft', child: Text('ft')),
                            ],
                            onChanged: (value) =>
                                setState(() => _heightUnit = value!),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGoalsPage() {
    return Padding(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Your Goals',
            style: Theme.of(
              context,
            ).textTheme.headlineMedium?.copyWith(fontWeight: FontWeight.bold),
          ),

          const SizedBox(height: AppConstants.defaultPadding),

          Text(
            'Tell us about your activity level and goals',
            style: Theme.of(context).textTheme.bodyLarge,
          ),

          const SizedBox(height: AppConstants.largePadding),

          Expanded(
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Activity Level',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),

                  const SizedBox(height: AppConstants.smallPadding),

                  ...ActivityLevel.values.map(
                    (level) => RadioListTile<String>(
                      title: Text(level.displayName),
                      subtitle: Text(level.description),
                      value: level.value,
                      groupValue: _activityLevel,
                      onChanged: (value) =>
                          setState(() => _activityLevel = value!),
                    ),
                  ),

                  const SizedBox(height: AppConstants.largePadding),

                  Text(
                    'Goal',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),

                  const SizedBox(height: AppConstants.smallPadding),

                  ...Goal.values.map(
                    (goal) => RadioListTile<String>(
                      title: Text(goal.displayName),
                      subtitle: Text(goal.description),
                      value: goal.value,
                      groupValue: _goal,
                      onChanged: (value) => setState(() => _goal = value!),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNavigationButtons() {
    return Padding(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Row(
        children: [
          if (_currentPage > 0)
            Expanded(
              child: CustomButton(
                text: 'Back',
                onPressed: _previousPage,
                isOutlined: true,
              ),
            ),

          if (_currentPage > 0)
            const SizedBox(width: AppConstants.defaultPadding),

          Expanded(
            child: CustomButton(
              text: _currentPage == 2 ? 'Get Started' : 'Next',
              onPressed: _nextPage,
              isLoading: _isLoading,
              icon: _currentPage == 2 ? Icons.check : Icons.arrow_forward,
            ),
          ),
        ],
      ),
    );
  }
}

enum ActivityLevel {
  sedentary('sedentary', 'Sedentary', 'Little to no exercise'),
  lightlyActive(
    'lightly_active',
    'Lightly Active',
    'Light exercise 1-3 days/week',
  ),
  moderatelyActive(
    'moderately_active',
    'Moderately Active',
    'Moderate exercise 3-5 days/week',
  ),
  veryActive('very_active', 'Very Active', 'Hard exercise 6-7 days/week'),
  extremelyActive(
    'extremely_active',
    'Extremely Active',
    'Very hard exercise, physical job',
  );

  const ActivityLevel(this.value, this.displayName, this.description);
  final String value;
  final String displayName;
  final String description;
}

enum Goal {
  lose('lose', 'Lose Weight', 'Create a caloric deficit'),
  maintain('maintain', 'Maintain Weight', 'Maintain current weight'),
  gain('gain', 'Gain Weight', 'Create a caloric surplus');

  const Goal(this.value, this.displayName, this.description);
  final String value;
  final String displayName;
  final String description;
}
